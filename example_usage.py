#!/usr/bin/env python3
"""
Example Usage Script for Aadhaar Validity Checker

This script demonstrates various ways to use the AadhaarValidityChecker class
with different configurations and scenarios.
"""

import json
import time
from aad<PERSON>ar_checker import <PERSON><PERSON><PERSON>arValidity<PERSON><PERSON><PERSON>


def example_1_basic_usage():
    """
    Example 1: Basic usage with visible browser
    """
    print("=" * 60)
    print("EXAMPLE 1: Basic Usage")
    print("=" * 60)
    
    # Create checker instance with visible browser for debugging
    checker = AadhaarValidityChecker(headless=False, debug=True)
    
    try:
        # Check the test Aadhaar number
        aadhaar_number = "************"
        print(f"Checking Aadhaar: {aadhaar_number}")
        
        result = checker.check_aadhaar_validity(aadhaar_number)
        
        # Display results
        print("\nResult:")
        print(json.dumps(result, indent=2))
        
        if result["success"]:
            print("\n✅ Validation successful!")
            print(f"Status: {result['validation_data'].get('status', 'Unknown')}")
        else:
            print(f"\n❌ Validation failed: {result['error']}")
    
    finally:
        checker.close()


def example_2_headless_mode():
    """
    Example 2: Headless mode for production use
    """
    print("\n" + "=" * 60)
    print("EXAMPLE 2: Headless Mode (Production)")
    print("=" * 60)
    
    # Create checker instance in headless mode
    checker = AadhaarValidityChecker(headless=True, debug=False)
    
    try:
        aadhaar_number = "************"
        print(f"Checking Aadhaar: {aadhaar_number} (headless mode)")
        
        start_time = time.time()
        result = checker.check_aadhaar_validity(aadhaar_number, max_retries=3)
        end_time = time.time()
        
        print(f"Execution time: {end_time - start_time:.2f} seconds")
        print(f"Success: {result['success']}")
        
        if not result["success"]:
            print(f"Error: {result['error']}")
    
    finally:
        checker.close()


def example_3_batch_processing():
    """
    Example 3: Batch processing multiple Aadhaar numbers
    """
    print("\n" + "=" * 60)
    print("EXAMPLE 3: Batch Processing")
    print("=" * 60)
    
    # List of Aadhaar numbers to check
    aadhaar_numbers = [
        "************",  # Test number
        "123456789012",  # Invalid number for testing
        "************"   # Another test number
    ]
    
    # Use headless mode for batch processing
    checker = AadhaarValidityChecker(headless=True, debug=False)
    
    results = []
    
    try:
        for i, aadhaar in enumerate(aadhaar_numbers, 1):
            print(f"\nProcessing {i}/{len(aadhaar_numbers)}: {aadhaar}")
            
            result = checker.check_aadhaar_validity(aadhaar, max_retries=2)
            results.append(result)
            
            # Brief summary
            status = "✅ Valid" if result["success"] else f"❌ {result.get('error_type', 'Invalid')}"
            print(f"  Result: {status}")
            
            # Small delay between requests to be respectful
            time.sleep(2)
        
        # Summary
        print(f"\n" + "-" * 40)
        print("BATCH PROCESSING SUMMARY")
        print("-" * 40)
        
        valid_count = sum(1 for r in results if r["success"])
        invalid_count = len(results) - valid_count
        
        print(f"Total processed: {len(results)}")
        print(f"Valid: {valid_count}")
        print(f"Invalid/Error: {invalid_count}")
        
        # Save results to file
        with open("batch_results.json", "w") as f:
            json.dump(results, f, indent=2)
        print(f"\nDetailed results saved to: batch_results.json")
    
    finally:
        checker.close()


def example_4_error_handling():
    """
    Example 4: Comprehensive error handling
    """
    print("\n" + "=" * 60)
    print("EXAMPLE 4: Error Handling")
    print("=" * 60)
    
    checker = AadhaarValidityChecker(headless=True, debug=False)
    
    # Test various error scenarios
    test_cases = [
        ("", "Empty input"),
        ("123", "Too short"),
        ("abcdefghijkl", "Non-numeric"),
        ("************", "Valid format")
    ]
    
    try:
        for aadhaar, description in test_cases:
            print(f"\nTesting: {description}")
            print(f"Input: '{aadhaar}'")
            
            try:
                result = checker.check_aadhaar_validity(aadhaar, max_retries=1)
                
                if result["success"]:
                    print("  ✅ Validation successful")
                else:
                    print(f"  ❌ Error: {result['error']}")
                    print(f"  Error Type: {result['error_type']}")
                
            except Exception as e:
                print(f"  💥 Exception: {str(e)}")
    
    finally:
        checker.close()


def example_5_custom_configuration():
    """
    Example 5: Custom configuration and advanced usage
    """
    print("\n" + "=" * 60)
    print("EXAMPLE 5: Custom Configuration")
    print("=" * 60)
    
    # Custom configuration
    checker = AadhaarValidityChecker(
        headless=False,  # Visible for demonstration
        debug=True       # Enable debug mode
    )
    
    try:
        aadhaar_number = "************"
        
        # Custom retry logic
        max_retries = 5
        print(f"Checking with {max_retries} max retries...")
        
        result = checker.check_aadhaar_validity(
            aadhaar_number, 
            max_retries=max_retries
        )
        
        # Detailed result analysis
        print("\nDetailed Result Analysis:")
        print("-" * 30)
        
        for key, value in result.items():
            if key == "validation_data" and isinstance(value, dict):
                print(f"{key}:")
                for sub_key, sub_value in value.items():
                    print(f"  {sub_key}: {sub_value}")
            else:
                print(f"{key}: {value}")
    
    finally:
        checker.close()


def interactive_example():
    """
    Interactive example - get Aadhaar number from user input
    """
    print("\n" + "=" * 60)
    print("INTERACTIVE EXAMPLE")
    print("=" * 60)
    
    try:
        aadhaar_input = input("Enter Aadhaar number to check (or press Enter for test number): ").strip()
        
        if not aadhaar_input:
            aadhaar_input = "************"
            print(f"Using test number: {aadhaar_input}")
        
        headless_choice = input("Run in headless mode? (y/n, default=n): ").strip().lower()
        headless = headless_choice in ['y', 'yes']
        
        print(f"\nChecking Aadhaar: {aadhaar_input}")
        print(f"Headless mode: {headless}")
        print("Processing...")
        
        checker = AadhaarValidityChecker(headless=headless, debug=not headless)
        
        try:
            result = checker.check_aadhaar_validity(aadhaar_input)
            
            print("\n" + "=" * 40)
            print("RESULT")
            print("=" * 40)
            print(json.dumps(result, indent=2))
            
        finally:
            checker.close()
    
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
    except Exception as e:
        print(f"\nError: {str(e)}")


def main():
    """
    Main function to run all examples
    """
    print("Aadhaar Validity Checker - Usage Examples")
    print("This script demonstrates various usage patterns.")
    print("\nNote: These examples may take several minutes to complete.")
    
    try:
        # Run examples
        example_1_basic_usage()
        
        input("\nPress Enter to continue to next example...")
        example_2_headless_mode()
        
        input("\nPress Enter to continue to batch processing example...")
        example_3_batch_processing()
        
        input("\nPress Enter to continue to error handling example...")
        example_4_error_handling()
        
        input("\nPress Enter to continue to custom configuration example...")
        example_5_custom_configuration()
        
        print("\n" + "=" * 60)
        print("ALL EXAMPLES COMPLETED")
        print("=" * 60)
        
        # Offer interactive mode
        interactive_choice = input("\nWould you like to try interactive mode? (y/n): ").strip().lower()
        if interactive_choice in ['y', 'yes']:
            interactive_example()
    
    except KeyboardInterrupt:
        print("\n\nExamples interrupted by user.")
    except Exception as e:
        print(f"\nError running examples: {str(e)}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        example_name = sys.argv[1].lower()
        
        if example_name == "1" or example_name == "basic":
            example_1_basic_usage()
        elif example_name == "2" or example_name == "headless":
            example_2_headless_mode()
        elif example_name == "3" or example_name == "batch":
            example_3_batch_processing()
        elif example_name == "4" or example_name == "error":
            example_4_error_handling()
        elif example_name == "5" or example_name == "custom":
            example_5_custom_configuration()
        elif example_name == "interactive":
            interactive_example()
        else:
            print("Usage: python example_usage.py [1|2|3|4|5|interactive]")
            print("  1/basic     - Basic usage example")
            print("  2/headless  - Headless mode example")
            print("  3/batch     - Batch processing example")
            print("  4/error     - Error handling example")
            print("  5/custom    - Custom configuration example")
            print("  interactive - Interactive mode")
    else:
        main()

# Installation Guide for Aadhaar Validity Checker

This guide provides step-by-step instructions to set up and run the Aadhaar Validity Checker.

## System Requirements

### Operating System
- Windows 10/11
- macOS 10.14+
- Linux (Ubuntu 18.04+, CentOS 7+, or equivalent)

### Software Requirements
- Python 3.7 or higher
- Google Chrome browser (latest version recommended)
- Internet connection

## Step 1: Install Python

### Windows
1. Download Python from https://www.python.org/downloads/
2. Run the installer and check "Add Python to PATH"
3. Verify installation: `python --version`

### macOS
```bash
# Using Homebrew (recommended)
brew install python

# Or download from python.org
```

### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3 python3-pip
```

## Step 2: Install Google Chrome

Download and install Google Chrome from https://www.google.com/chrome/

## Step 3: Install Tesseract OCR

### Windows
1. Download Tesseract installer from: https://github.com/UB-Mannheim/tesseract/wiki
2. Run the installer (use default installation path: `C:\Program Files\Tesseract-OCR\`)
3. Add Tesseract to your system PATH:
   - Open System Properties → Advanced → Environment Variables
   - Add `C:\Program Files\Tesseract-OCR` to your PATH variable
4. Restart your command prompt/terminal

### macOS
```bash
# Using Homebrew
brew install tesseract

# Using MacPorts
sudo port install tesseract
```

### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install tesseract-ocr
sudo apt install libtesseract-dev
```

### Linux (CentOS/RHEL)
```bash
sudo yum install epel-release
sudo yum install tesseract tesseract-devel
```

## Step 4: Verify Tesseract Installation

```bash
tesseract --version
```

You should see output similar to:
```
tesseract 5.3.0
```

## Step 5: Set Up the Project

1. **Download/Clone the project files**
   ```bash
   # If using git
   git clone <repository-url>
   cd aadhaar-automation
   
   # Or extract the downloaded files to a folder
   ```

2. **Create a virtual environment (recommended)**
   ```bash
   python -m venv aadhaar_env
   
   # Activate the virtual environment
   # Windows:
   aadhaar_env\Scripts\activate
   
   # macOS/Linux:
   source aadhaar_env/bin/activate
   ```

3. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## Step 6: Verify Installation

Run the test script to verify everything is working:

```bash
# Basic test
python test_aadhaar_checker.py basic

# Or run all tests
python test_aadhaar_checker.py all
```

## Step 7: Run the Main Script

```bash
# Run with the test Aadhaar number
python aadhaar_checker.py

# Run with a custom Aadhaar number
python aadhaar_checker.py ************

# Run multiple test scenarios
python aadhaar_checker.py --test

# Show help
python aadhaar_checker.py --help
```

## Troubleshooting

### Common Issues and Solutions

#### 1. "tesseract is not recognized as an internal or external command"
**Solution**: Tesseract is not in your PATH. 
- Windows: Add `C:\Program Files\Tesseract-OCR` to your PATH
- macOS/Linux: Reinstall using package manager

#### 2. "ChromeDriver not found"
**Solution**: The script uses webdriver-manager to automatically download ChromeDriver. Ensure you have internet connection.

#### 3. "Permission denied" errors
**Solution**: 
- Run terminal/command prompt as administrator (Windows)
- Use `sudo` for installation commands (Linux/macOS)
- Check file permissions

#### 4. "Module not found" errors
**Solution**: 
- Ensure virtual environment is activated
- Reinstall requirements: `pip install -r requirements.txt`
- Check Python version: `python --version`

#### 5. Chrome browser issues
**Solution**:
- Update Chrome to the latest version
- Try running in headless mode: `AadhaarValidityChecker(headless=True)`
- Check if Chrome is properly installed

#### 6. Captcha solving failures
**Solution**:
- Increase retry count: `max_retries=5`
- Run in visible mode for debugging: `headless=False`
- Check Tesseract installation and PATH

#### 7. Network/timeout issues
**Solution**:
- Check internet connection
- Try increasing timeout values
- Check firewall settings

### Debug Mode

For troubleshooting, enable debug mode:

```python
checker = AadhaarValidityChecker(headless=False, debug=True)
```

This will:
- Show the browser window
- Save captcha images for inspection
- Provide detailed logging

### Getting Help

If you encounter issues:

1. Check the error message carefully
2. Run in debug mode to see what's happening
3. Verify all dependencies are installed correctly
4. Check system requirements
5. Try running the test script first

### Performance Tips

1. **Use headless mode** for faster execution:
   ```python
   checker = AadhaarValidityChecker(headless=True)
   ```

2. **Adjust retry counts** based on your needs:
   ```python
   result = checker.check_aadhaar_validity(aadhaar, max_retries=5)
   ```

3. **Close the checker** when done:
   ```python
   checker.close()
   ```

## Security Considerations

1. **Use responsibly**: Only check Aadhaar numbers you have permission to validate
2. **Rate limiting**: Don't make too many requests in quick succession
3. **Data privacy**: Don't store or log sensitive Aadhaar information
4. **Compliance**: Ensure compliance with UIDAI terms of service and local laws

## Next Steps

After successful installation:

1. Read the main README.md for usage examples
2. Review the code documentation
3. Test with your specific use cases
4. Consider integrating into your applications

For advanced usage and integration examples, see the main documentation.

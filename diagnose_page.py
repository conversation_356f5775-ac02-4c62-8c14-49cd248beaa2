#!/usr/bin/env python3
"""
Page Diagnostic Script

This script inspects the actual UIDAI page structure to understand
why captcha extraction is failing.
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import json


def diagnose_uidai_page():
    """Diagnose the UIDAI page structure."""
    print("🔍 Diagnosing UIDAI page structure...")
    
    # Setup Chrome options
    options = Options()
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")
    
    driver = None
    try:
        # Create WebDriver
        driver = webdriver.Chrome(options=options)
        driver.implicitly_wait(10)
        
        print("✅ WebDriver created successfully")
        
        # Navigate to UIDAI website
        url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        print(f"🌐 Navigating to: {url}")
        
        driver.get(url)
        
        # Wait for page to load
        WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        time.sleep(5)  # Additional wait for dynamic content
        
        print("✅ Page loaded successfully")
        print(f"📄 Page title: {driver.title}")
        print(f"🔗 Current URL: {driver.current_url}")
        
        # Get page source length
        page_source = driver.page_source
        print(f"📊 Page source length: {len(page_source):,} characters")
        
        # Check for common elements
        print("\n🔍 Checking for common elements:")
        
        # Check for forms
        forms = driver.find_elements(By.TAG_NAME, "form")
        print(f"  📝 Forms found: {len(forms)}")
        
        # Check for inputs
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"  📥 Input fields found: {len(inputs)}")
        
        for i, input_elem in enumerate(inputs[:5]):  # Show first 5 inputs
            input_type = input_elem.get_attribute("type") or "text"
            input_name = input_elem.get_attribute("name") or "unnamed"
            input_id = input_elem.get_attribute("id") or "no-id"
            input_placeholder = input_elem.get_attribute("placeholder") or "no-placeholder"
            print(f"    Input {i+1}: type='{input_type}', name='{input_name}', id='{input_id}', placeholder='{input_placeholder}'")
        
        # Check for images
        images = driver.find_elements(By.TAG_NAME, "img")
        print(f"  🖼️ Images found: {len(images)}")
        
        for i, img in enumerate(images[:3]):  # Show first 3 images
            src = img.get_attribute("src") or "no-src"
            alt = img.get_attribute("alt") or "no-alt"
            print(f"    Image {i+1}: src='{src[:100]}...', alt='{alt}'")
        
        # Check for canvas elements
        canvas_elements = driver.find_elements(By.TAG_NAME, "canvas")
        print(f"  🎨 Canvas elements found: {len(canvas_elements)}")
        
        # Check for buttons
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"  🔘 Buttons found: {len(buttons)}")
        
        for i, button in enumerate(buttons[:3]):  # Show first 3 buttons
            text = button.text or "no-text"
            onclick = button.get_attribute("onclick") or "no-onclick"
            print(f"    Button {i+1}: text='{text}', onclick='{onclick[:50]}...'")
        
        # Check for specific captcha-related elements
        print("\n🔍 Checking for captcha-related elements:")
        
        captcha_selectors = [
            "img[src*='captcha']",
            "img[src*='data:image']",
            "[class*='captcha']",
            "[id*='captcha']",
            "[name*='captcha']",
            "canvas"
        ]
        
        for selector in captcha_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                print(f"  {selector}: {len(elements)} elements found")
                
                for i, elem in enumerate(elements[:2]):  # Show first 2 elements
                    tag_name = elem.tag_name
                    class_attr = elem.get_attribute("class") or "no-class"
                    id_attr = elem.get_attribute("id") or "no-id"
                    print(f"    Element {i+1}: <{tag_name}> class='{class_attr}' id='{id_attr}'")
            except Exception as e:
                print(f"  {selector}: Error - {e}")
        
        # Check page text for error messages
        print("\n📝 Checking page text for clues:")
        body_text = driver.find_element(By.TAG_NAME, "body").text
        
        # Look for common error/blocking messages
        error_keywords = [
            "blocked", "access denied", "not available", "maintenance",
            "captcha", "verification", "security", "robot", "bot"
        ]
        
        found_keywords = []
        for keyword in error_keywords:
            if keyword.lower() in body_text.lower():
                found_keywords.append(keyword)
        
        if found_keywords:
            print(f"  ⚠️ Found keywords: {', '.join(found_keywords)}")
        else:
            print("  ✅ No blocking keywords found")
        
        # Show first 500 characters of body text
        print(f"\n📄 First 500 characters of page text:")
        print(f"'{body_text[:500]}...'")
        
        # Save page source for inspection
        with open("page_source_debug.html", "w", encoding="utf-8") as f:
            f.write(page_source)
        print(f"\n💾 Page source saved to: page_source_debug.html")
        
        # Take a screenshot
        driver.save_screenshot("page_screenshot_debug.png")
        print(f"📸 Screenshot saved to: page_screenshot_debug.png")
        
        # Check if we can find any form to submit
        print("\n🔍 Looking for forms to interact with:")
        
        for i, form in enumerate(forms):
            print(f"  Form {i+1}:")
            form_inputs = form.find_elements(By.TAG_NAME, "input")
            form_buttons = form.find_elements(By.TAG_NAME, "button")
            
            print(f"    Inputs in form: {len(form_inputs)}")
            print(f"    Buttons in form: {len(form_buttons)}")
            
            for j, inp in enumerate(form_inputs[:3]):
                inp_type = inp.get_attribute("type") or "text"
                inp_name = inp.get_attribute("name") or "unnamed"
                print(f"      Input {j+1}: type='{inp_type}', name='{inp_name}'")
        
        print("\n✅ Diagnosis complete!")
        
    except Exception as e:
        print(f"❌ Error during diagnosis: {e}")
        
    finally:
        if driver:
            input("\nPress Enter to close browser and continue...")
            driver.quit()


if __name__ == "__main__":
    diagnose_uidai_page()

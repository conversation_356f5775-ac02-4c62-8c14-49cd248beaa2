#!/usr/bin/env python3
"""
Test script for Aadhaar Validity Checker

This script provides various test scenarios to validate the functionality
of the AadhaarValidityChecker class.
"""

import json
import time
from aadhaar_checker import <PERSON>ad<PERSON>arValidityChecker


def test_basic_functionality():
    """Test basic functionality with the provided test Aadhaar number."""
    print("=" * 60)
    print("TEST 1: Basic Functionality Test")
    print("=" * 60)
    
    test_aadhaar = "************"
    
    print(f"Testing with Aadhaar: {test_aadhaar}")
    
    # Test with visible browser for debugging
    checker = AadhaarValidityChecker(headless=False, debug=True)
    
    try:
        result = checker.check_aadhaar_validity(test_aadhaar, max_retries=3)
        
        print("\nResult:")
        print(json.dumps(result, indent=2))
        
        # Validate result structure
        assert isinstance(result, dict), "Result should be a dictionary"
        assert "success" in result, "Result should contain 'success' field"
        assert "timestamp" in result, "Result should contain 'timestamp' field"
        
        if result["success"]:
            assert "validation_data" in result, "Success result should contain 'validation_data'"
            print("✅ Test PASSED: Basic functionality working")
        else:
            assert "error" in result, "Error result should contain 'error' field"
            assert "error_type" in result, "Error result should contain 'error_type' field"
            print(f"⚠️ Test COMPLETED: Validation failed as expected - {result['error']}")
        
    except Exception as e:
        print(f"❌ Test FAILED: {str(e)}")
        
    finally:
        checker.close()


def test_headless_mode():
    """Test functionality in headless mode."""
    print("\n" + "=" * 60)
    print("TEST 2: Headless Mode Test")
    print("=" * 60)
    
    test_aadhaar = "************"
    
    print(f"Testing headless mode with Aadhaar: {test_aadhaar}")
    
    # Test with headless browser
    checker = AadhaarValidityChecker(headless=True, debug=False)
    
    try:
        start_time = time.time()
        result = checker.check_aadhaar_validity(test_aadhaar, max_retries=2)
        end_time = time.time()
        
        print(f"\nExecution time: {end_time - start_time:.2f} seconds")
        print(f"Result success: {result.get('success', False)}")
        print(f"Error type: {result.get('error_type', 'N/A')}")
        
        print("✅ Test PASSED: Headless mode working")
        
    except Exception as e:
        print(f"❌ Test FAILED: {str(e)}")
        
    finally:
        checker.close()


def test_invalid_inputs():
    """Test with various invalid inputs."""
    print("\n" + "=" * 60)
    print("TEST 3: Invalid Input Handling")
    print("=" * 60)
    
    invalid_inputs = [
        ("", "Empty string"),
        ("123", "Too short"),
        ("12345678901234567890", "Too long"),
        ("abcdefghijkl", "Non-numeric"),
        ("123-456-789", "Invalid format"),
        (None, "None value")
    ]
    
    checker = AadhaarValidityChecker(headless=True, debug=False)
    
    try:
        for aadhaar, description in invalid_inputs:
            print(f"\nTesting: {description} - '{aadhaar}'")
            
            result = checker.check_aadhaar_validity(aadhaar, max_retries=1)
            
            # Should always fail for invalid inputs
            assert not result.get("success", True), f"Should fail for {description}"
            assert "error" in result, f"Should contain error for {description}"
            
            print(f"  ✅ Correctly rejected: {result['error']}")
        
        print("\n✅ Test PASSED: Invalid input handling working correctly")
        
    except Exception as e:
        print(f"❌ Test FAILED: {str(e)}")
        
    finally:
        checker.close()


def test_retry_logic():
    """Test retry logic with intentionally difficult scenarios."""
    print("\n" + "=" * 60)
    print("TEST 4: Retry Logic Test")
    print("=" * 60)
    
    test_aadhaar = "************"
    
    print(f"Testing retry logic with Aadhaar: {test_aadhaar}")
    print("This test may take longer as it tests retry scenarios...")
    
    checker = AadhaarValidityChecker(headless=True, debug=True)
    
    try:
        # Test with different retry counts
        for retry_count in [1, 2, 3]:
            print(f"\nTesting with max_retries = {retry_count}")
            
            start_time = time.time()
            result = checker.check_aadhaar_validity(test_aadhaar, max_retries=retry_count)
            end_time = time.time()
            
            print(f"  Time taken: {end_time - start_time:.2f} seconds")
            print(f"  Success: {result.get('success', False)}")
            print(f"  Error type: {result.get('error_type', 'N/A')}")
        
        print("\n✅ Test PASSED: Retry logic functioning")
        
    except Exception as e:
        print(f"❌ Test FAILED: {str(e)}")
        
    finally:
        checker.close()


def run_all_tests():
    """Run all test scenarios."""
    print("Starting comprehensive test suite for Aadhaar Validity Checker")
    print("This may take several minutes to complete...")
    
    try:
        test_basic_functionality()
        test_headless_mode()
        test_invalid_inputs()
        test_retry_logic()
        
        print("\n" + "=" * 60)
        print("ALL TESTS COMPLETED")
        print("=" * 60)
        print("✅ Test suite execution finished successfully")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Test suite interrupted by user")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {str(e)}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        test_name = sys.argv[1].lower()
        
        if test_name == "basic":
            test_basic_functionality()
        elif test_name == "headless":
            test_headless_mode()
        elif test_name == "invalid":
            test_invalid_inputs()
        elif test_name == "retry":
            test_retry_logic()
        elif test_name == "all":
            run_all_tests()
        else:
            print("Usage: python test_aadhaar_checker.py [basic|headless|invalid|retry|all]")
    else:
        run_all_tests()

#!/usr/bin/env python3
"""
Advanced Captcha Solver with Multiple Techniques

This script provides various advanced techniques for solving captchas,
including noise removal, character segmentation, and machine learning approaches.
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract
import base64
from io import BytesIO
import os
from datetime import datetime


class AdvancedCaptchaSolver:
    """
    Advanced captcha solver with multiple preprocessing and recognition techniques.
    """
    
    def __init__(self, debug=True):
        self.debug = debug
        self.debug_dir = "captcha_debug"
        if self.debug and not os.path.exists(self.debug_dir):
            os.makedirs(self.debug_dir)
    
    def solve_captcha_from_base64(self, base64_string):
        """
        Solve captcha from base64 string using multiple techniques.
        
        Args:
            base64_string (str): Base64 encoded image
            
        Returns:
            dict: Results from different solving methods
        """
        try:
            # Decode base64 to image
            if base64_string.startswith("data:"):
                base64_string = base64_string.split(",", 1)[1]
            
            image_data = base64.b64decode(base64_string)
            image = Image.open(BytesIO(image_data))
            
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            print(f"📸 Original image size: {image.size}")
            
            # Save original for reference
            if self.debug:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                original_path = os.path.join(self.debug_dir, f"original_{timestamp}.png")
                image.save(original_path)
                print(f"💾 Saved original: {original_path}")
            
            # Try multiple solving approaches
            results = {}
            
            # Method 1: Basic preprocessing
            results['basic'] = self._solve_with_basic_preprocessing(image, timestamp)
            
            # Method 2: Advanced noise removal
            results['advanced'] = self._solve_with_advanced_preprocessing(image, timestamp)
            
            # Method 3: Color-based isolation
            results['color_isolation'] = self._solve_with_color_isolation(image, timestamp)
            
            # Method 4: Character segmentation
            results['segmentation'] = self._solve_with_segmentation(image, timestamp)
            
            # Method 5: Multiple thresholding
            results['multi_threshold'] = self._solve_with_multi_threshold(image, timestamp)
            
            # Analyze results and pick the best one
            best_result = self._analyze_results(results)
            
            return {
                'best_result': best_result,
                'all_results': results,
                'confidence': self._calculate_confidence(results, best_result)
            }
            
        except Exception as e:
            print(f"❌ Error solving captcha: {str(e)}")
            return {'error': str(e)}
    
    def _solve_with_basic_preprocessing(self, image, timestamp):
        """Basic preprocessing approach."""
        try:
            # Scale up
            width, height = image.size
            scale_factor = max(300 / width, 80 / height)
            new_size = (int(width * scale_factor), int(height * scale_factor))
            processed = image.resize(new_size, Image.LANCZOS)
            
            # Convert to grayscale
            processed = processed.convert('L')
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(processed)
            processed = enhancer.enhance(2.5)
            
            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(processed)
            processed = enhancer.enhance(2.0)
            
            if self.debug:
                debug_path = os.path.join(self.debug_dir, f"basic_{timestamp}.png")
                processed.save(debug_path)
            
            # OCR
            config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
            text = pytesseract.image_to_string(processed, config=config).strip()
            cleaned = self._clean_text(text)
            
            return {'text': cleaned, 'method': 'basic', 'processed_image': debug_path if self.debug else None}
            
        except Exception as e:
            return {'error': str(e), 'method': 'basic'}
    
    def _solve_with_advanced_preprocessing(self, image, timestamp):
        """Advanced preprocessing with OpenCV."""
        try:
            # Convert to OpenCV format
            img_array = np.array(image)
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            
            # Scale up significantly
            height, width = img_array.shape
            scale_factor = max(400 / width, 100 / height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img_array = cv2.resize(img_array, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # Bilateral filter to reduce noise while preserving edges
            img_array = cv2.bilateralFilter(img_array, 9, 75, 75)
            
            # CLAHE for better contrast
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
            img_array = clahe.apply(img_array)
            
            # Adaptive thresholding
            binary = cv2.adaptiveThreshold(
                img_array, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            
            # Morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
            
            # Remove small noise
            binary = cv2.medianBlur(binary, 3)
            
            processed = Image.fromarray(binary)
            
            if self.debug:
                debug_path = os.path.join(self.debug_dir, f"advanced_{timestamp}.png")
                processed.save(debug_path)
            
            # Multiple OCR attempts
            configs = [
                r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',
                r'--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',
                r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
            ]
            
            best_text = ""
            for config in configs:
                try:
                    text = pytesseract.image_to_string(processed, config=config).strip()
                    cleaned = self._clean_text(text)
                    if len(cleaned) > len(best_text):
                        best_text = cleaned
                except:
                    continue
            
            return {'text': best_text, 'method': 'advanced', 'processed_image': debug_path if self.debug else None}
            
        except Exception as e:
            return {'error': str(e), 'method': 'advanced'}
    
    def _solve_with_color_isolation(self, image, timestamp):
        """Color-based text isolation."""
        try:
            img_array = np.array(image)
            
            # Scale up
            height, width = img_array.shape[:2]
            scale_factor = max(350 / width, 90 / height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img_array = cv2.resize(img_array, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # Convert to HSV
            hsv = cv2.cvtColor(img_array, cv2.COLOR_RGB2HSV)
            
            # Define range for text (usually dark)
            lower_text = np.array([0, 0, 0])
            upper_text = np.array([180, 255, 100])
            
            # Create mask
            mask = cv2.inRange(hsv, lower_text, upper_text)
            
            # Morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            
            # Remove noise
            mask = cv2.medianBlur(mask, 3)
            
            processed = Image.fromarray(mask)
            
            if self.debug:
                debug_path = os.path.join(self.debug_dir, f"color_{timestamp}.png")
                processed.save(debug_path)
            
            # OCR
            config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
            text = pytesseract.image_to_string(processed, config=config).strip()
            cleaned = self._clean_text(text)
            
            return {'text': cleaned, 'method': 'color_isolation', 'processed_image': debug_path if self.debug else None}
            
        except Exception as e:
            return {'error': str(e), 'method': 'color_isolation'}
    
    def _solve_with_segmentation(self, image, timestamp):
        """Character segmentation approach."""
        try:
            # Convert to grayscale and scale up
            img_array = np.array(image.convert('L'))
            height, width = img_array.shape
            scale_factor = max(400 / width, 100 / height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img_array = cv2.resize(img_array, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # Threshold
            _, binary = cv2.threshold(img_array, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # Find contours (potential characters)
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter and sort contours
            char_contours = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                if w > 10 and h > 15 and w < new_width * 0.8 and h < new_height * 0.8:
                    char_contours.append((x, y, w, h))
            
            # Sort by x-coordinate (left to right)
            char_contours.sort(key=lambda x: x[0])
            
            # Extract individual characters and OCR each
            result_chars = []
            for i, (x, y, w, h) in enumerate(char_contours):
                char_img = binary[y:y+h, x:x+w]
                char_pil = Image.fromarray(255 - char_img)  # Invert for OCR
                
                if self.debug:
                    char_path = os.path.join(self.debug_dir, f"char_{timestamp}_{i}.png")
                    char_pil.save(char_path)
                
                try:
                    config = r'--oem 3 --psm 10 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
                    char_text = pytesseract.image_to_string(char_pil, config=config).strip()
                    if char_text and len(char_text) == 1:
                        result_chars.append(char_text)
                except:
                    continue
            
            final_text = ''.join(result_chars)
            cleaned = self._clean_text(final_text)
            
            if self.debug:
                debug_path = os.path.join(self.debug_dir, f"segmented_{timestamp}.png")
                Image.fromarray(255 - binary).save(debug_path)
            
            return {'text': cleaned, 'method': 'segmentation', 'processed_image': debug_path if self.debug else None}
            
        except Exception as e:
            return {'error': str(e), 'method': 'segmentation'}
    
    def _solve_with_multi_threshold(self, image, timestamp):
        """Multiple thresholding techniques."""
        try:
            img_array = np.array(image.convert('L'))
            
            # Scale up
            height, width = img_array.shape
            scale_factor = max(350 / width, 90 / height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img_array = cv2.resize(img_array, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # Try different thresholding methods
            thresholding_methods = [
                ('otsu', lambda img: cv2.threshold(img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]),
                ('adaptive_mean', lambda img: cv2.adaptiveThreshold(img, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 11, 2)),
                ('adaptive_gaussian', lambda img: cv2.adaptiveThreshold(img, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)),
            ]
            
            best_text = ""
            for method_name, threshold_func in thresholding_methods:
                try:
                    thresholded = threshold_func(img_array)
                    processed = Image.fromarray(thresholded)
                    
                    if self.debug:
                        debug_path = os.path.join(self.debug_dir, f"thresh_{method_name}_{timestamp}.png")
                        processed.save(debug_path)
                    
                    config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
                    text = pytesseract.image_to_string(processed, config=config).strip()
                    cleaned = self._clean_text(text)
                    
                    if len(cleaned) > len(best_text):
                        best_text = cleaned
                        
                except:
                    continue
            
            return {'text': best_text, 'method': 'multi_threshold', 'processed_image': None}
            
        except Exception as e:
            return {'error': str(e), 'method': 'multi_threshold'}
    
    def _clean_text(self, text):
        """Clean extracted text."""
        if not text:
            return ""
        
        # Remove non-alphanumeric characters
        import re
        cleaned = re.sub(r'[^a-zA-Z0-9]', '', text.strip())
        
        # Filter reasonable length
        if 3 <= len(cleaned) <= 10:
            return cleaned
        
        return ""
    
    def _analyze_results(self, results):
        """Analyze all results and pick the best one."""
        valid_results = []
        
        for method, result in results.items():
            if 'text' in result and result['text'] and len(result['text']) >= 4:
                valid_results.append((result['text'], method))
        
        if not valid_results:
            return None
        
        # If multiple results, prefer the most common one
        from collections import Counter
        text_counts = Counter([text for text, _ in valid_results])
        
        if text_counts:
            most_common = text_counts.most_common(1)[0][0]
            return most_common
        
        return valid_results[0][0] if valid_results else None
    
    def _calculate_confidence(self, results, best_result):
        """Calculate confidence based on result consistency."""
        if not best_result:
            return 0
        
        valid_results = [r['text'] for r in results.values() if 'text' in r and r['text']]
        
        if not valid_results:
            return 0
        
        # Count how many methods agree
        agreement_count = sum(1 for text in valid_results if text == best_result)
        confidence = (agreement_count / len(valid_results)) * 100
        
        return confidence


def main():
    """Test the advanced captcha solver."""
    solver = AdvancedCaptchaSolver(debug=True)
    
    # Example usage - you would pass the actual base64 string here
    print("🔧 Advanced Captcha Solver Ready")
    print("📝 Usage: solver.solve_captcha_from_base64(base64_string)")
    print("🎯 This solver uses 5 different techniques to maximize accuracy")


if __name__ == "__main__":
    main()

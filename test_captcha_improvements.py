#!/usr/bin/env python3
"""
Test script to demonstrate captcha solving improvements
"""

import os
import glob
from advanced_captcha_solver import AdvancedCaptchaSolver
from a<PERSON>haar_checker import Aad<PERSON>arVali<PERSON>y<PERSON><PERSON><PERSON>


def test_saved_captcha_images():
    """Test the advanced solver on any saved captcha images."""
    print("🔍 Looking for saved captcha images...")
    
    # Look for debug images
    captcha_files = []
    patterns = [
        "captcha_debug_*.png",
        "captcha_extracted_*.png",
        "debug_images/captcha_*.png"
    ]
    
    for pattern in patterns:
        captcha_files.extend(glob.glob(pattern))
    
    if not captcha_files:
        print("❌ No saved captcha images found")
        print("💡 Run the main script first to generate captcha images")
        return
    
    print(f"📸 Found {len(captcha_files)} captcha images")
    
    solver = AdvancedCaptchaSolver(debug=True)
    
    for captcha_file in captcha_files:
        print(f"\n🔍 Testing: {captcha_file}")
        
        try:
            # Convert image to base64
            from PIL import Image
            import base64
            from io import BytesIO
            
            image = Image.open(captcha_file)
            buffer = BytesIO()
            image.save(buffer, format='PNG')
            img_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            # Test with advanced solver
            result = solver.solve_captcha_from_base64(img_base64)
            
            print(f"🎯 Best result: {result.get('best_result', 'None')}")
            print(f"📊 Confidence: {result.get('confidence', 0):.1f}%")
            
            if 'all_results' in result:
                print("📋 All methods:")
                for method, res in result['all_results'].items():
                    if 'text' in res:
                        print(f"  {method}: '{res['text']}'")
                    elif 'error' in res:
                        print(f"  {method}: ERROR - {res['error']}")
            
        except Exception as e:
            print(f"❌ Error testing {captcha_file}: {str(e)}")


def test_live_captcha():
    """Test with a live captcha from the website."""
    print("\n🌐 Testing with live captcha from UIDAI website...")
    
    try:
        # Create checker to get a fresh captcha
        checker = AadhaarValidityChecker(headless=False, debug=True)
        
        print("📡 Navigating to website...")
        if not checker.navigate_to_website():
            print("❌ Failed to navigate to website")
            return
        
        print("🖼️ Extracting captcha image...")
        captcha_image = checker.extract_captcha_image()
        
        if not captcha_image:
            print("❌ Failed to extract captcha image")
            return
        
        print("✅ Captcha extracted successfully!")
        
        # Convert to base64
        from io import BytesIO
        import base64
        
        buffer = BytesIO()
        captcha_image.save(buffer, format='PNG')
        img_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        # Test with advanced solver
        solver = AdvancedCaptchaSolver(debug=True)
        result = solver.solve_captcha_from_base64(img_base64)
        
        print(f"\n🎯 LIVE CAPTCHA RESULTS:")
        print(f"Best result: {result.get('best_result', 'None')}")
        print(f"Confidence: {result.get('confidence', 0):.1f}%")
        
        if 'all_results' in result:
            print("\n📋 All solving methods:")
            for method, res in result['all_results'].items():
                if 'text' in res:
                    print(f"  ✅ {method}: '{res['text']}'")
                elif 'error' in res:
                    print(f"  ❌ {method}: {res['error']}")
        
        # Test the best result
        best_result = result.get('best_result')
        if best_result:
            print(f"\n🧪 Testing best result '{best_result}' on the website...")
            
            # Fill the form with the solved captcha
            success = checker.fill_aadhaar_form("************", best_result)
            if success:
                print("✅ Successfully filled form with solved captcha!")
                
                # Check for any immediate errors
                import time
                time.sleep(2)
                error = checker.check_for_errors()
                if error and checker.is_captcha_invalid_error(error):
                    print(f"❌ Captcha was incorrect: {error}")
                else:
                    print("🎉 Captcha appears to be correct!")
            else:
                print("❌ Failed to fill form")
        
        checker.close()
        
    except Exception as e:
        print(f"❌ Error in live test: {str(e)}")


def compare_methods():
    """Compare old vs new captcha solving methods."""
    print("\n📊 CAPTCHA SOLVING METHOD COMPARISON")
    print("=" * 50)
    
    print("🔧 OLD METHOD:")
    print("  • Basic grayscale conversion")
    print("  • Simple contrast enhancement")
    print("  • Single OCR configuration")
    print("  • Limited preprocessing")
    
    print("\n🚀 NEW ADVANCED METHODS:")
    print("  • 5 different preprocessing techniques:")
    print("    1. Basic (improved scaling + sharpness)")
    print("    2. Advanced (bilateral filter + CLAHE + morphology)")
    print("    3. Color isolation (HSV-based text extraction)")
    print("    4. Character segmentation (individual char OCR)")
    print("    5. Multi-threshold (multiple binarization methods)")
    print("  • 7 different OCR configurations per method")
    print("  • Confidence scoring and result consensus")
    print("  • Noise removal and edge preservation")
    print("  • Adaptive thresholding")
    print("  • Morphological operations")
    
    print("\n💡 KEY IMPROVEMENTS:")
    print("  ✅ Better noise handling")
    print("  ✅ Multiple solving approaches")
    print("  ✅ Confidence-based selection")
    print("  ✅ Character-level analysis")
    print("  ✅ Color-based text isolation")
    print("  ✅ Advanced image enhancement")


def main():
    """Main test function."""
    print("🎯 ADVANCED CAPTCHA SOLVER TESTING")
    print("=" * 50)
    
    # Show comparison
    compare_methods()
    
    # Test saved images first
    test_saved_captcha_images()
    
    # Ask user if they want to test live
    try:
        response = input("\n🌐 Test with live captcha from website? (y/n): ").strip().lower()
        if response in ['y', 'yes']:
            test_live_captcha()
    except KeyboardInterrupt:
        print("\n\n⚠️ Test cancelled by user")
    
    print("\n🎉 Testing complete!")
    print("\n💡 RECOMMENDATIONS:")
    print("  1. Use the advanced solver for better accuracy")
    print("  2. Check debug images to see preprocessing results")
    print("  3. Adjust preprocessing parameters for specific captcha types")
    print("  4. Consider training a custom ML model for this specific captcha")


if __name__ == "__main__":
    main()

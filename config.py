"""
Configuration file for Aadhaar Validity Checker

This file contains configurable settings that can be modified
without changing the main code.
"""

# Website Configuration
UIDAI_URL = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"

# Browser Configuration
BROWSER_CONFIG = {
    "headless": False,  # Set to True for headless mode
    "window_size": "1920,1080",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "page_load_timeout": 30,
    "implicit_wait": 10
}

# Retry Configuration
RETRY_CONFIG = {
    "max_retries": 3,
    "retry_delay": 2,  # seconds between retries
    "captcha_retry_delay": 3  # seconds after captcha refresh
}

# Timeout Configuration
TIMEOUT_CONFIG = {
    "page_load": 30,
    "element_wait": 20,
    "result_wait": 15,
    "captcha_solve": 10
}

# OCR Configuration
OCR_CONFIG = {
    "tesseract_config": r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',
    "image_scale_factor": 3,  # Scale factor for image resizing
    "contrast_enhancement": 2.0,
    "preprocessing_methods": ["basic", "enhanced", "aggressive"]
}

# Selector Configuration
SELECTORS = {
    "captcha_containers": [
        ".auth-form__captcha-field-container",
        ".auth-form_captcha-field-container",
        ".auth-formcaptcha-box",
        ".captcha-container",
        ".captcha"
    ],
    "captcha_images": [
        ".auth-form__captcha-field-container img",
        ".auth-form_captcha-field-container img",
        ".auth-formcaptcha-box img",
        ".captcha-container img",
        "img[src*='data:image']",
        ".captcha img"
    ],
    "captcha_canvas": [
        ".auth-form__captcha-field-container canvas",
        ".auth-form_captcha-field-container canvas",
        ".auth-formcaptcha-box canvas",
        ".captcha-container canvas",
        "canvas"
    ],
    "aadhaar_input": [
        "input[name='uid']",
        "input[name*='aadhaar']",
        "input[id*='aadhaar']",
        "input[placeholder*='aadhaar']",
        "input[placeholder*='Aadhaar']",
        "input[type='text']",
        "input[type='number']",
        ".form-control",
        "#aadhaarNumber",
        "#aadhaar_number"
    ],
    "captcha_input": [
        "input[name='captcha']",
        ".auth-form__captcha-field",
        ".auth-form_captcha-field",
        "input[name*='captcha']",
        "input[id*='captcha']",
        "input[placeholder*='captcha']",
        "input[placeholder*='Captcha']",
        ".captcha-input",
        "#captcha",
        "#captchaCode"
    ],
    "submit_button": [
        "button[type='submit']",
        "input[type='submit']",
        "button.btn-primary",
        "button.submit",
        ".submit-btn",
        "#submit"
    ],
    "success_indicators": [
        ".success",
        ".valid",
        ".result-success",
        "[class*='success']",
        "[class*='valid']"
    ],
    "error_indicators": [
        ".error",
        ".invalid",
        ".result-error",
        "[class*='error']",
        "[class*='invalid']",
        ".alert-danger"
    ],
    "captcha_refresh": [
        ".captcha-refresh",
        ".refresh-captcha",
        "button[onclick*='captcha']",
        "button[onclick*='refresh']",
        ".fa-refresh",
        ".fa-reload"
    ]
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",  # DEBUG, INFO, WARNING, ERROR
    "format": "%(asctime)s - %(levelname)s - %(message)s",
    "save_debug_images": True,
    "debug_image_path": "debug_images/"
}

# Validation Configuration
VALIDATION_CONFIG = {
    "aadhaar_length": 12,
    "min_captcha_length": 3,
    "max_captcha_length": 10,
    "captcha_size_threshold": {
        "min_width": 50,
        "max_width": 300,
        "min_height": 20,
        "max_height": 100
    }
}

# Error Messages
ERROR_MESSAGES = {
    "invalid_aadhaar_format": "Invalid Aadhaar number format. Must be 12 digits.",
    "navigation_failed": "Failed to load UIDAI website",
    "page_load_timeout": "Page load timeout",
    "captcha_extraction_failed": "Failed to extract captcha image",
    "captcha_solving_failed": "Failed to solve captcha after multiple attempts",
    "form_submission_failed": "Failed to submit validation form",
    "result_extraction_failed": "Could not extract validation results",
    "max_retries_exceeded": "All retry attempts failed",
    "webdriver_error": "WebDriver error occurred",
    "unexpected_error": "Unexpected error occurred"
}

# Success Keywords
SUCCESS_KEYWORDS = [
    "aadhaar number is valid",
    "valid aadhaar",
    "verification successful",
    "valid",
    "success",
    "verified"
]

# Error Keywords
ERROR_KEYWORDS = [
    "aadhaar number is invalid",
    "invalid aadhaar",
    "not found",
    "verification failed",
    "invalid",
    "error",
    "failed"
]

# Captcha Error Keywords
CAPTCHA_ERROR_KEYWORDS = [
    "captcha",
    "verification code",
    "invalid code",
    "wrong code",
    "incorrect code"
]

# Chrome Options
CHROME_OPTIONS = [
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--disable-blink-features=AutomationControlled",
    "--disable-extensions",
    "--disable-plugins",
    "--disable-images",  # Uncomment to disable image loading for faster execution
    # "--proxy-server=your-proxy:port",  # Uncomment and configure if using proxy
]

# Chrome Experimental Options
CHROME_EXPERIMENTAL_OPTIONS = {
    "excludeSwitches": ["enable-automation"],
    "useAutomationExtension": False,
    # "prefs": {
    #     "profile.default_content_setting_values.notifications": 2,
    #     "profile.default_content_settings.popups": 0,
    #     "profile.managed_default_content_settings.images": 2
    # }
}

# Development/Debug Settings
DEBUG_SETTINGS = {
    "save_page_source": False,
    "save_screenshots": False,
    "screenshot_path": "screenshots/",
    "verbose_logging": False,
    "step_by_step_mode": False  # Pause between major steps for debugging
}

# Performance Settings
PERFORMANCE_SETTINGS = {
    "disable_images": False,  # Disable image loading for faster execution
    "disable_css": False,     # Disable CSS loading
    "disable_javascript": False,  # Disable JavaScript (may break functionality)
    "connection_timeout": 30,
    "read_timeout": 30
}

# Rate Limiting (to be respectful to the server)
RATE_LIMITING = {
    "delay_between_requests": 2,  # seconds
    "max_requests_per_minute": 20,
    "respect_robots_txt": True
}

# File Paths
FILE_PATHS = {
    "debug_images": "debug_images/",
    "screenshots": "screenshots/",
    "logs": "logs/",
    "results": "results/"
}

# Feature Flags
FEATURE_FLAGS = {
    "enable_captcha_refresh": True,
    "enable_multiple_preprocessing": True,
    "enable_canvas_extraction": True,
    "enable_css_background_extraction": True,
    "enable_generic_base64_search": True,
    "enable_form_validation": True,
    "enable_result_validation": True
}

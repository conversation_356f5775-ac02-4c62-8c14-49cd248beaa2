#!/usr/bin/env python3
"""
Focused test to extract and analyze the captcha image
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import base64
from PIL import Image
from io import BytesIO


def test_captcha_extraction():
    """Test captcha image extraction specifically."""
    print("🔍 Testing captcha image extraction...")
    
    # Setup Chrome options
    options = Options()
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")
    
    driver = None
    try:
        # Create WebDriver
        driver = webdriver.Chrome(options=options)
        driver.implicitly_wait(10)
        
        print("✅ WebDriver created successfully")
        
        # Navigate to UIDAI website
        url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        print(f"🌐 Navigating to: {url}")
        
        driver.get(url)
        
        # Wait for page to load
        WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        time.sleep(3)  # Additional wait for dynamic content
        
        print("✅ Page loaded successfully")
        
        # Find the captcha container
        print("\n🔍 Looking for captcha container...")
        
        try:
            container = driver.find_element(By.CSS_SELECTOR, ".auth-form__captcha-field-container")
            print("✅ Found captcha container")
            
            # Find all images within the container
            images = container.find_elements(By.TAG_NAME, "img")
            print(f"📸 Found {len(images)} images in container")
            
            for i, img in enumerate(images):
                print(f"\n🖼️ Analyzing image {i+1}:")
                
                # Get image attributes
                src = img.get_attribute("src")
                alt = img.get_attribute("alt") or "no-alt"
                width = img.get_attribute("width") or "auto"
                height = img.get_attribute("height") or "auto"
                class_attr = img.get_attribute("class") or "no-class"
                
                print(f"  Source: {src[:100] if src else 'None'}...")
                print(f"  Alt: {alt}")
                print(f"  Dimensions: {width} x {height}")
                print(f"  Class: {class_attr}")
                
                # Check if it's a base64 image
                if src and src.startswith("data:image"):
                    print("  🎯 This is a base64 image!")
                    
                    try:
                        # Extract and decode the image
                        base64_data = src.split(",", 1)[1]
                        image_data = base64.b64decode(base64_data)
                        
                        # Create PIL Image
                        pil_image = Image.open(BytesIO(image_data))
                        
                        print(f"  📊 Image size: {pil_image.size}")
                        print(f"  📊 Image mode: {pil_image.mode}")
                        
                        # Save the image for inspection
                        filename = f"captcha_extracted_{i+1}.png"
                        pil_image.save(filename)
                        print(f"  💾 Saved image as: {filename}")
                        
                        # Check if this looks like a captcha
                        width, height = pil_image.size
                        if 50 <= width <= 300 and 20 <= height <= 100:
                            print("  ✅ Image dimensions suggest this is likely a captcha!")
                        else:
                            print("  ⚠️ Image dimensions don't match typical captcha size")
                        
                    except Exception as e:
                        print(f"  ❌ Error processing base64 image: {e}")
                
                elif src and src.startswith("http"):
                    print("  📡 This is a remote image URL")
                    
                    # Try to get the actual image size from the browser
                    try:
                        actual_width = driver.execute_script("return arguments[0].naturalWidth;", img)
                        actual_height = driver.execute_script("return arguments[0].naturalHeight;", img)
                        print(f"  📊 Actual size: {actual_width} x {actual_height}")
                        
                        if 50 <= actual_width <= 300 and 20 <= actual_height <= 100:
                            print("  ✅ Image dimensions suggest this is likely a captcha!")
                        else:
                            print("  ⚠️ Image dimensions don't match typical captcha size")
                    except Exception as e:
                        print(f"  ❌ Error getting image dimensions: {e}")
                
                else:
                    print("  ❓ Unknown image type")
                
                # Check if image is visible
                if img.is_displayed():
                    print("  👁️ Image is visible")
                else:
                    print("  🙈 Image is not visible")
            
            # Also check for any canvas elements
            print("\n🎨 Checking for canvas elements...")
            canvas_elements = container.find_elements(By.TAG_NAME, "canvas")
            print(f"Found {len(canvas_elements)} canvas elements")
            
            for i, canvas in enumerate(canvas_elements):
                print(f"  Canvas {i+1}:")
                width = canvas.get_attribute("width") or "auto"
                height = canvas.get_attribute("height") or "auto"
                print(f"    Dimensions: {width} x {height}")
                
                try:
                    # Try to extract canvas data
                    canvas_data = driver.execute_script("return arguments[0].toDataURL('image/png');", canvas)
                    if canvas_data:
                        print("    ✅ Successfully extracted canvas data")
                        
                        # Save canvas image
                        base64_data = canvas_data.split(",", 1)[1]
                        image_data = base64.b64decode(base64_data)
                        pil_image = Image.open(BytesIO(image_data))
                        
                        filename = f"captcha_canvas_{i+1}.png"
                        pil_image.save(filename)
                        print(f"    💾 Saved canvas as: {filename}")
                    else:
                        print("    ❌ Could not extract canvas data")
                except Exception as e:
                    print(f"    ❌ Error extracting canvas: {e}")
            
        except Exception as e:
            print(f"❌ Error finding captcha container: {e}")
        
        print("\n✅ Captcha extraction test complete!")
        
        # Keep browser open for manual inspection
        input("\nPress Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        
    finally:
        if driver:
            driver.quit()


if __name__ == "__main__":
    test_captcha_extraction()

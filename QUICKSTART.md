# Quick Start Guide - Aadhaar Validity Checker

Get up and running with the Aadhaar Validity Checker in just a few minutes!

## 🚀 Quick Installation

### Prerequisites
- Python 3.7+
- Google Chrome browser
- Internet connection

### 1. Install Tesseract OCR

**Windows:**
```bash
# Download and install from: https://github.com/UB-Mannheim/tesseract/wiki
# Add to PATH: C:\Program Files\Tesseract-OCR
```

**macOS:**
```bash
brew install tesseract
```

**Linux:**
```bash
sudo apt install tesseract-ocr
```

### 2. Install Python Dependencies
```bash
pip install -r requirements.txt
```

### 3. Verify Installation
```bash
python test_aadhaar_checker.py basic
```

## 🎯 Quick Usage

### Basic Example
```python
from aadhaar_checker import AadhaarValidityChecker

# Create checker
checker = AadhaarValidityChecker(headless=True)

# Check Aadhaar
result = checker.check_aadhaar_validity("************")

# Print result
print(result)

# Clean up
checker.close()
```

### Command Line Usage
```bash
# Run with test number
python aadhaar_checker.py

# Run with custom number
python aadhaar_checker.py ************

# Run tests
python aadhaar_checker.py --test
```

## 📁 Project Structure

```
aadhaar-automation/
├── aadhaar_checker.py      # Main checker class
├── config.py               # Configuration settings
├── requirements.txt        # Python dependencies
├── test_aadhaar_checker.py # Test suite
├── example_usage.py        # Usage examples
├── README.md              # Detailed documentation
├── INSTALLATION.md        # Installation guide
└── QUICKSTART.md          # This file
```

## 🔧 Configuration

Edit `config.py` to customize:
- Browser settings (headless mode, timeouts)
- OCR parameters
- Retry logic
- Selectors for web elements

## 📊 Expected Output

### Success Response
```json
{
  "success": true,
  "aadhaar_number": "************",
  "validation_data": {
    "status": "Valid",
    "message": "Aadhaar number is valid"
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Invalid Aadhaar number format",
  "error_type": "ValidationError",
  "aadhaar_number": "123",
  "timestamp": "2024-01-01 12:00:00"
}
```

## 🐛 Troubleshooting

### Common Issues

**"tesseract not found"**
```bash
# Ensure Tesseract is in PATH
tesseract --version
```

**"ChromeDriver not found"**
- Script auto-downloads ChromeDriver
- Ensure internet connection

**Captcha solving fails**
```python
# Try visible mode for debugging
checker = AadhaarValidityChecker(headless=False, debug=True)
```

**Timeout errors**
```python
# Increase retry count
result = checker.check_aadhaar_validity(aadhaar, max_retries=5)
```

## 🎮 Interactive Mode

```bash
python example_usage.py interactive
```

## 📝 Examples

Run different examples:
```bash
python example_usage.py basic      # Basic usage
python example_usage.py headless   # Headless mode
python example_usage.py batch      # Batch processing
python example_usage.py error      # Error handling
```

## ⚡ Performance Tips

1. **Use headless mode** for faster execution
2. **Batch processing** with delays between requests
3. **Configure timeouts** based on your network
4. **Enable debug mode** only when troubleshooting

## 🔒 Important Notes

- **Legal compliance**: Only check Aadhaar numbers you have permission to validate
- **Rate limiting**: Don't make too many requests quickly
- **Data privacy**: Don't store sensitive information
- **Respectful usage**: Follow UIDAI terms of service

## 📞 Support

If you encounter issues:

1. Check error messages carefully
2. Run in debug mode: `debug=True`
3. Verify all dependencies are installed
4. Check the detailed logs
5. Try the test suite first

## 🚀 Next Steps

1. Read the full [README.md](README.md) for detailed documentation
2. Check [INSTALLATION.md](INSTALLATION.md) for comprehensive setup
3. Explore [example_usage.py](example_usage.py) for advanced patterns
4. Customize [config.py](config.py) for your needs
5. Run [test_aadhaar_checker.py](test_aadhaar_checker.py) to validate setup

---

**Ready to start? Run this command:**
```bash
python aadhaar_checker.py
```

Happy validating! 🎉

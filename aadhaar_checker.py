"""
<PERSON><PERSON><PERSON><PERSON> Validity Checker

A Python automation script to check <PERSON><PERSON><PERSON><PERSON> validity by scraping the official UIDAI website.
Uses Selenium for web automation and Tesseract OCR for captcha solving.
"""

import time
import base64
import re
import json
from datetime import datetime
from io import BytesIO
import logging

# Third-party imports
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

import pytesseract

# Set Tesseract path for Windows if not in PATH
import os
if os.name == 'nt':  # Windows
    tesseract_path = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
    if os.path.exists(tesseract_path):
        pytesseract.pytesseract.tesseract_cmd = tesseract_path
from PIL import Image, ImageEnhance, ImageFilter
import cv2
import numpy as np
from advanced_captcha_solver import AdvancedCaptchaSolver

# Import configuration
try:
    from config import *
except ImportError:
    # Fallback configuration if config.py is not available
    UIDAI_URL = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
    RETRY_CONFIG = {"max_retries": 3, "retry_delay": 2}
    TIMEOUT_CONFIG = {"page_load": 30, "element_wait": 20}
    OCR_CONFIG = {"tesseract_config": r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'}
    LOGGING_CONFIG = {"level": "INFO", "format": "%(asctime)s - %(levelname)s - %(message)s"}


class AadhaarValidityChecker:
    """
    Main class for checking Aadhaar validity through UIDAI website automation.
    """
    
    def __init__(self, headless=False, debug=False):
        """
        Initialize the Aadhaar validity checker.
        
        Args:
            headless (bool): Run browser in headless mode
            debug (bool): Enable debug logging and save captcha images
        """
        self.headless = headless
        self.debug = debug
        self.driver = None
        self.wait = None
        self.target_url = UIDAI_URL

        # Initialize advanced captcha solver
        self.advanced_captcha_solver = AdvancedCaptchaSolver(debug=debug)

        # Configure logging
        logging.basicConfig(
            level=logging.INFO if not debug else logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Initialize WebDriver
        self.setup_driver()
    
    def setup_driver(self):
        """
        Configure and initialize Chrome WebDriver with appropriate options.
        """
        try:
            chrome_options = Options()

            if self.headless:
                chrome_options.add_argument("--headless")

            # Essential Chrome options for stability
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Set user agent to avoid detection
            chrome_options.add_argument(
                "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )

            # Try multiple approaches to initialize WebDriver
            driver_initialized = False

            # Approach 1: Try with automatic ChromeDriver management
            try:
                self.logger.info("Attempting to initialize WebDriver with ChromeDriverManager...")
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                driver_initialized = True
                self.logger.info("WebDriver initialized successfully with ChromeDriverManager")
            except Exception as e1:
                self.logger.warning(f"ChromeDriverManager failed: {str(e1)}")

                # Approach 2: Clear cache and try again
                try:
                    self.logger.info("Clearing ChromeDriver cache and retrying...")
                    import shutil
                    import os
                    cache_path = os.path.expanduser("~/.wdm")
                    if os.path.exists(cache_path):
                        shutil.rmtree(cache_path)

                    service = Service(ChromeDriverManager().install())
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                    driver_initialized = True
                    self.logger.info("WebDriver initialized successfully after cache clear")
                except Exception as e2:
                    self.logger.warning(f"Cache clear approach failed: {str(e2)}")

                    # Approach 3: Try without service (let Selenium find ChromeDriver)
                    try:
                        self.logger.info("Attempting to initialize WebDriver without explicit service...")
                        self.driver = webdriver.Chrome(options=chrome_options)
                        driver_initialized = True
                        self.logger.info("WebDriver initialized successfully without explicit service")
                    except Exception as e3:
                        self.logger.error(f"All WebDriver initialization approaches failed")
                        raise Exception(f"WebDriver initialization failed. Tried multiple approaches:\n"
                                      f"1. ChromeDriverManager: {str(e1)}\n"
                                      f"2. Cache clear: {str(e2)}\n"
                                      f"3. Default service: {str(e3)}")

            if driver_initialized:
                # Execute script to remove webdriver property
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

                # Set timeouts
                self.driver.implicitly_wait(10)
                self.driver.set_page_load_timeout(30)

                # Initialize WebDriverWait
                self.wait = WebDriverWait(self.driver, 20)

                self.logger.info("WebDriver setup completed successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize WebDriver: {str(e)}")
            raise Exception(f"WebDriver initialization failed: {str(e)}")
    
    def navigate_to_website(self):
        """
        Navigate to the UIDAI Aadhaar validity check website.
        """
        try:
            self.logger.info(f"Navigating to {self.target_url}")
            self.driver.get(self.target_url)
            
            # Wait for page to load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # Add small delay to ensure full page load
            time.sleep(2)
            
            self.logger.info("Successfully navigated to UIDAI website")
            return True
            
        except TimeoutException:
            self.logger.error("Timeout while loading the website")
            return False
        except Exception as e:
            self.logger.error(f"Error navigating to website: {str(e)}")
            return False
    
    def close(self):
        """
        Clean up WebDriver resources.
        """
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("WebDriver closed successfully")
            except Exception as e:
                self.logger.error(f"Error closing WebDriver: {str(e)}")


    def extract_captcha_image(self):
        """
        Extract captcha image from various possible sources on the page.

        Returns:
            PIL.Image or None: The extracted captcha image
        """
        try:
            self.logger.info("Attempting to extract captcha image")

            # Method 1: Look for img tag with base64 src in captcha container
            captcha_img = self._extract_from_img_tag()
            if captcha_img:
                self.logger.info("Successfully extracted captcha from img tag")
                return captcha_img

            # Method 2: Look for canvas element
            captcha_img = self._extract_from_canvas()
            if captcha_img:
                self.logger.info("Successfully extracted captcha from canvas")
                return captcha_img

            # Method 3: Look for CSS background-image
            captcha_img = self._extract_from_css_background()
            if captcha_img:
                self.logger.info("Successfully extracted captcha from CSS background")
                return captcha_img

            # Method 4: Generic search for any base64 images
            captcha_img = self._extract_generic_base64()
            if captcha_img:
                self.logger.info("Successfully extracted captcha from generic base64 search")
                return captcha_img

            self.logger.error("Failed to extract captcha image from any source")
            return None

        except Exception as e:
            self.logger.error(f"Error extracting captcha image: {str(e)}")
            return None

    def _extract_from_img_tag(self):
        """
        Extract captcha image from img tag with base64 src.
        """
        try:
            # Look for captcha container - prioritize the correct selector
            captcha_selectors = [
                ".auth-form__captcha-field-container img",
                ".auth-form_captcha-field-container img",
                ".auth-formcaptcha-box img",
                ".captcha-container img",
                "img[src*='data:image']",
                ".captcha img",
                "img"  # Fallback to any image
            ]

            for selector in captcha_selectors:
                try:
                    img_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for img in img_elements:
                        src = img.get_attribute("src")
                        if src and (src.startswith("data:image") or src.startswith("data:application/image")):
                            return self.decode_base64_image(src)
                except:
                    continue

            return None

        except Exception as e:
            self.logger.debug(f"Error in _extract_from_img_tag: {str(e)}")
            return None

    def _extract_from_canvas(self):
        """
        Extract captcha image from canvas element.
        """
        try:
            canvas_selectors = [
                ".auth-form__captcha-field-container canvas",
                ".auth-form_captcha-field-container canvas",
                ".auth-formcaptcha-box canvas",
                ".captcha-container canvas",
                "canvas"
            ]

            for selector in canvas_selectors:
                try:
                    canvas_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for canvas in canvas_elements:
                        # Execute JavaScript to get canvas data as base64
                        canvas_data = self.driver.execute_script(
                            "return arguments[0].toDataURL('image/png');", canvas
                        )
                        if canvas_data:
                            return self.decode_base64_image(canvas_data)
                except:
                    continue

            return None

        except Exception as e:
            self.logger.debug(f"Error in _extract_from_canvas: {str(e)}")
            return None

    def _extract_from_css_background(self):
        """
        Extract captcha image from CSS background-image property.
        """
        try:
            bg_selectors = [
                ".auth-form__captcha-field-container",
                ".auth-form_captcha-field-container",
                ".auth-formcaptcha-box",
                ".captcha-container",
                ".captcha"
            ]

            for selector in bg_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        bg_image = element.value_of_css_property("background-image")
                        if bg_image and ("data:image" in bg_image or "data:application/image" in bg_image):
                            # Extract base64 from CSS url() function
                            match = re.search(r'url\("?(data:(?:image|application/image)[^"]*)"?\)', bg_image)
                            if match:
                                return self.decode_base64_image(match.group(1))
                except:
                    continue

            return None

        except Exception as e:
            self.logger.debug(f"Error in _extract_from_css_background: {str(e)}")
            return None

    def _extract_generic_base64(self):
        """
        Generic search for any base64 images on the page.
        """
        try:
            # Execute JavaScript to find all base64 images
            script = """
            var images = [];
            var allElements = document.querySelectorAll('*');
            for (var i = 0; i < allElements.length; i++) {
                var el = allElements[i];
                var src = el.getAttribute('src');
                var bg = window.getComputedStyle(el).backgroundImage;

                if (src && (src.indexOf('data:image') === 0 || src.indexOf('data:application/image') === 0)) {
                    images.push(src);
                }
                if (bg && (bg.indexOf('data:image') !== -1 || bg.indexOf('data:application/image') !== -1)) {
                    var match = bg.match(/url\\("?(data:(?:image|application\\/image)[^"]*)"?\\)/);
                    if (match) images.push(match[1]);
                }
            }
            return images;
            """

            base64_images = self.driver.execute_script(script)

            for base64_str in base64_images:
                try:
                    img = self.decode_base64_image(base64_str)
                    if img and self._is_likely_captcha(img):
                        return img
                except:
                    continue

            return None

        except Exception as e:
            self.logger.debug(f"Error in _extract_generic_base64: {str(e)}")
            return None

    def _is_likely_captcha(self, image):
        """
        Heuristic to determine if an image is likely a captcha.

        Args:
            image (PIL.Image): Image to check

        Returns:
            bool: True if image appears to be a captcha
        """
        try:
            width, height = image.size

            # Typical captcha dimensions
            if 50 <= width <= 300 and 20 <= height <= 100:
                return True

            # Check aspect ratio
            aspect_ratio = width / height
            if 1.5 <= aspect_ratio <= 6:
                return True

            return False

        except:
            return False

    def decode_base64_image(self, base64_string):
        """
        Convert base64 string to PIL Image.

        Args:
            base64_string (str): Base64 encoded image string

        Returns:
            PIL.Image or None: Decoded image
        """
        try:
            # Handle different base64 formats
            if base64_string.startswith("data:image") or base64_string.startswith("data:application/image"):
                # Remove data URL prefix
                base64_string = base64_string.split(",", 1)[1]

            # Decode base64
            image_data = base64.b64decode(base64_string)

            # Create PIL Image
            image = Image.open(BytesIO(image_data))

            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')

            self.logger.debug(f"Successfully decoded image: {image.size}")

            # Save debug image if enabled
            if self.debug:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                debug_path = f"captcha_debug_{timestamp}.png"
                image.save(debug_path)
                self.logger.debug(f"Saved debug image: {debug_path}")

            return image

        except Exception as e:
            self.logger.error(f"Error decoding base64 image: {str(e)}")
            return None

    def solve_captcha(self, image):
        """
        Use advanced OCR to solve captcha image with multiple techniques.

        Args:
            image (PIL.Image): Captcha image to solve

        Returns:
            str or None: Extracted captcha text
        """
        try:
            if not image:
                return None

            self.logger.info("🔍 Attempting to solve captcha using Advanced OCR System")

            # Convert PIL image to base64 for the advanced solver
            from io import BytesIO
            import base64

            buffer = BytesIO()
            image.save(buffer, format='PNG')
            img_base64 = base64.b64encode(buffer.getvalue()).decode()

            # Use the advanced captcha solver
            result = self.advanced_captcha_solver.solve_captcha_from_base64(img_base64)

            if 'error' in result:
                self.logger.error(f"Advanced captcha solver error: {result['error']}")
                return self._fallback_solve_captcha(image)

            best_result = result.get('best_result')
            confidence = result.get('confidence', 0)
            all_results = result.get('all_results', {})

            if best_result and len(best_result) >= 4:
                self.logger.info(f"🎯 Advanced solver SUCCESS: '{best_result}' (confidence: {confidence:.1f}%)")

                # Log all method results for debugging
                if self.debug:
                    self.logger.debug("📊 All solving methods results:")
                    for method, res in all_results.items():
                        if 'text' in res and res['text']:
                            self.logger.debug(f"  ✅ {method}: '{res['text']}'")
                        elif 'error' in res:
                            self.logger.debug(f"  ❌ {method}: {res['error']}")

                return best_result

            elif best_result and len(best_result) >= 3:
                self.logger.warning(f"⚠️ Short result from advanced solver: '{best_result}' (confidence: {confidence:.1f}%)")
                # For short results, try fallback as well and compare
                fallback_result = self._fallback_solve_captcha(image)
                if fallback_result and len(fallback_result) >= len(best_result):
                    self.logger.info(f"🔄 Using fallback result: '{fallback_result}'")
                    return fallback_result
                return best_result

            else:
                self.logger.warning("❌ Advanced solver failed to find good result, trying fallback")
                return self._fallback_solve_captcha(image)

        except Exception as e:
            self.logger.error(f"Error in advanced captcha solving: {str(e)}")
            return self._fallback_solve_captcha(image)

    def _fallback_solve_captcha(self, image):
        """
        Fallback captcha solving using original preprocessing methods.

        Args:
            image (PIL.Image): Captcha image to solve

        Returns:
            str or None: Extracted captcha text
        """
        try:
            self.logger.info("🔄 Using fallback captcha solving methods")

            # Try multiple preprocessing approaches (ordered from simple to complex)
            preprocessing_methods = [
                self._preprocess_basic,
                self._preprocess_enhanced,
                self._preprocess_aggressive
            ]

            for i, preprocess_func in enumerate(preprocessing_methods):
                try:
                    processed_image = preprocess_func(image)
                    captcha_text = self._extract_text_with_ocr(processed_image)

                    if captcha_text and len(captcha_text) >= 4:
                        self.logger.info(f"✅ Fallback method {i+1} SUCCESS: '{captcha_text}'")
                        return captcha_text
                    elif captcha_text and len(captcha_text) >= 3:
                        self.logger.debug(f"⚠️ Fallback method {i+1} short result: '{captcha_text}'")

                except Exception as e:
                    self.logger.debug(f"❌ Fallback method {i+1} failed: {str(e)}")
                    continue

            self.logger.error("❌ All fallback methods failed")
            return None

        except Exception as e:
            self.logger.error(f"Error in fallback captcha solving: {str(e)}")
            return None

    def _preprocess_basic(self, image):
        """
        Basic image preprocessing for OCR.
        """
        # Resize image for better OCR
        width, height = image.size
        if width < 200:
            scale_factor = 200 / width
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            image = image.resize((new_width, new_height), Image.LANCZOS)

        # Convert to grayscale
        image = image.convert('L')

        # Enhance contrast
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(2.0)

        return image

    def _preprocess_enhanced(self, image):
        """
        Enhanced image preprocessing with noise reduction.
        """
        # Start with basic preprocessing
        image = self._preprocess_basic(image)

        # Convert PIL to OpenCV format
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # Apply Gaussian blur to reduce noise
        cv_image = cv2.GaussianBlur(cv_image, (1, 1), 0)

        # Apply threshold
        _, cv_image = cv2.threshold(cv_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Convert back to PIL
        image = Image.fromarray(cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB))

        return image

    def _preprocess_aggressive(self, image):
        """
        Aggressive preprocessing for difficult captchas.
        """
        # Start with enhanced preprocessing
        image = self._preprocess_enhanced(image)

        # Convert to OpenCV
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # Morphological operations to clean up
        kernel = np.ones((2, 2), np.uint8)
        cv_image = cv2.morphologyEx(cv_image, cv2.MORPH_CLOSE, kernel)
        cv_image = cv2.morphologyEx(cv_image, cv2.MORPH_OPEN, kernel)

        # Convert back to PIL
        image = Image.fromarray(cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB))

        return image

    def _preprocess_super_advanced(self, image):
        """
        Super advanced preprocessing with multiple noise removal techniques.
        """
        try:
            # Convert PIL to OpenCV format
            img_array = np.array(image)
            if len(img_array.shape) == 3:
                img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)

            # Scale up significantly for better OCR
            height, width = img_array.shape
            scale_factor = max(400 / width, 100 / height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img_array = cv2.resize(img_array, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

            # Apply bilateral filter to reduce noise while preserving edges
            img_array = cv2.bilateralFilter(img_array, 9, 75, 75)

            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
            img_array = clahe.apply(img_array)

            # Multiple thresholding approaches
            # 1. Otsu's thresholding
            _, otsu = cv2.threshold(img_array, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 2. Adaptive thresholding
            adaptive = cv2.adaptiveThreshold(
                img_array, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, 2
            )

            # Combine both approaches
            combined = cv2.bitwise_and(otsu, adaptive)

            # Morphological operations to clean text
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            combined = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
            combined = cv2.morphologyEx(combined, cv2.MORPH_OPEN, kernel)

            # Remove small noise components
            num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(combined, connectivity=8)
            min_size = 30  # Minimum component size
            cleaned = np.zeros_like(combined)

            for i in range(1, num_labels):
                if stats[i, cv2.CC_STAT_AREA] >= min_size:
                    cleaned[labels == i] = 255

            # Final denoising
            cleaned = cv2.medianBlur(cleaned, 3)

            # Convert back to PIL
            return Image.fromarray(cleaned)

        except Exception as e:
            self.logger.debug(f"Error in super advanced preprocessing: {str(e)}")
            return self._preprocess_aggressive(image)

    def _preprocess_color_isolation(self, image):
        """
        Color-based preprocessing to isolate text from background.
        """
        try:
            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')

            img_array = np.array(image)

            # Scale up first
            height, width = img_array.shape[:2]
            scale_factor = max(300 / width, 80 / height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img_array = cv2.resize(img_array, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

            # Convert to different color spaces and try to isolate text
            # Method 1: HSV-based isolation
            hsv = cv2.cvtColor(img_array, cv2.COLOR_RGB2HSV)

            # Define range for dark text (adjust based on captcha characteristics)
            lower_text = np.array([0, 0, 0])
            upper_text = np.array([180, 255, 120])

            text_mask = cv2.inRange(hsv, lower_text, upper_text)

            # Method 2: LAB-based isolation
            lab = cv2.cvtColor(img_array, cv2.COLOR_RGB2LAB)
            l_channel = lab[:, :, 0]

            # Threshold on L channel (lightness)
            _, lab_thresh = cv2.threshold(l_channel, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

            # Combine both masks
            combined_mask = cv2.bitwise_or(text_mask, lab_thresh)

            # Apply morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)

            # Remove noise
            combined_mask = cv2.medianBlur(combined_mask, 3)

            return Image.fromarray(combined_mask)

        except Exception as e:
            self.logger.debug(f"Error in color isolation preprocessing: {str(e)}")
            return self._preprocess_basic(image)

    def _extract_text_with_ocr(self, image):
        """
        Extract text from preprocessed image using multiple Tesseract configurations.
        """
        try:
            # Save debug image if enabled
            if self.debug:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                debug_path = f"ocr_debug_{timestamp}.png"
                image.save(debug_path)
                self.logger.debug(f"Saved OCR debug image: {debug_path}")

            # Multiple Tesseract configurations to try
            ocr_configs = [
                # Standard alphanumeric
                r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',

                # Single text line
                r'--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',

                # Single word
                r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',

                # Raw line (no assumptions)
                r'--oem 3 --psm 13 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',

                # Default with different OEM
                r'--oem 1 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',

                # Digits only (in case it's numeric)
                r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789',

                # Letters only (in case it's alphabetic)
                r'--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
            ]

            best_result = None
            best_confidence = 0

            for i, config in enumerate(ocr_configs):
                try:
                    # Extract text with current config
                    text = pytesseract.image_to_string(image, config=config)
                    cleaned_text = self._clean_captcha_text(text)

                    if cleaned_text and len(cleaned_text) >= 3:
                        # Try to get confidence score
                        try:
                            data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)
                            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
                        except:
                            avg_confidence = 50  # Default confidence

                        self.logger.debug(f"OCR config {i+1}: '{cleaned_text}' (confidence: {avg_confidence:.1f})")

                        # Keep track of best result
                        if avg_confidence > best_confidence or (avg_confidence == best_confidence and len(cleaned_text) > len(best_result or "")):
                            best_result = cleaned_text
                            best_confidence = avg_confidence

                        # If we get high confidence, use it immediately
                        if avg_confidence > 80 and len(cleaned_text) >= 4:
                            self.logger.info(f"High confidence OCR result: '{cleaned_text}' ({avg_confidence:.1f}%)")
                            return cleaned_text

                except Exception as e:
                    self.logger.debug(f"OCR config {i+1} failed: {str(e)}")
                    continue

            if best_result:
                self.logger.info(f"Best OCR result: '{best_result}' (confidence: {best_confidence:.1f}%)")
                return best_result

            return None

        except Exception as e:
            self.logger.debug(f"OCR extraction failed: {str(e)}")
            return None

    def _clean_captcha_text(self, text):
        """
        Clean and validate extracted captcha text.
        """
        if not text:
            return None

        # Remove whitespace and special characters
        cleaned = re.sub(r'[^a-zA-Z0-9]', '', text.strip())

        # Filter out very short or very long results
        if len(cleaned) < 3 or len(cleaned) > 10:
            return None

        return cleaned

    def validate_aadhaar_number(self, aadhaar_number):
        """
        Basic validation of Aadhaar number format.

        Args:
            aadhaar_number (str): Aadhaar number to validate

        Returns:
            bool: True if format is valid
        """
        if not aadhaar_number:
            return False

        # Remove spaces and hyphens
        cleaned = re.sub(r'[\s-]', '', str(aadhaar_number))

        # Check if it's exactly 12 digits
        if not re.match(r'^\d{12}$', cleaned):
            return False

        return True

    def fill_aadhaar_form(self, aadhaar_number, captcha_text):
        """
        Fill the Aadhaar validation form with number and captcha.

        Args:
            aadhaar_number (str): Aadhaar number to check
            captcha_text (str): Solved captcha text

        Returns:
            bool: True if form was filled and submitted successfully
        """
        try:
            self.logger.info("Filling Aadhaar validation form")

            # Find and fill Aadhaar number field
            aadhaar_filled = self._fill_aadhaar_field(aadhaar_number)
            if not aadhaar_filled:
                self.logger.error("Failed to fill Aadhaar number field")
                return False

            # Find and fill captcha field
            captcha_filled = self._fill_captcha_field(captcha_text)
            if not captcha_filled:
                self.logger.error("Failed to fill captcha field")
                return False

            # Submit the form
            submitted = self._submit_form()
            if not submitted:
                self.logger.error("Failed to submit form")
                return False

            self.logger.info("Form filled and submitted successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error filling form: {str(e)}")
            return False

    def _fill_aadhaar_field(self, aadhaar_number):
        """
        Find and fill the Aadhaar number input field.
        """
        try:
            # Common selectors for Aadhaar input field
            aadhaar_selectors = [
                "input[name='uid']",
                "input[name*='aadhaar']",
                "input[id*='aadhaar']",
                "input[placeholder*='aadhaar']",
                "input[placeholder*='Aadhaar']",
                "input[type='text']",
                "input[type='number']",
                ".form-control",
                "#aadhaarNumber",
                "#aadhaar_number"
            ]

            for selector in aadhaar_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        # Check if this looks like an Aadhaar field
                        if self._is_aadhaar_field(element):
                            # Clear and fill the field
                            element.clear()
                            element.send_keys(aadhaar_number)

                            # Verify the value was set
                            if element.get_attribute('value') == aadhaar_number:
                                self.logger.info("Successfully filled Aadhaar number field")
                                return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.debug(f"Error in _fill_aadhaar_field: {str(e)}")
            return False

    def _is_aadhaar_field(self, element):
        """
        Heuristic to determine if an input field is for Aadhaar number.
        """
        try:
            # Check various attributes
            attributes_to_check = ['name', 'id', 'placeholder', 'class']

            for attr in attributes_to_check:
                value = element.get_attribute(attr)
                if value and 'aadhaar' in value.lower():
                    return True

            # Check if it's a numeric field with appropriate maxlength
            input_type = element.get_attribute('type')
            maxlength = element.get_attribute('maxlength')

            if input_type in ['text', 'number'] and maxlength == '12':
                return True

            # Check if it's the first visible text/number input
            if element.is_displayed() and input_type in ['text', 'number']:
                return True

            return False

        except:
            return False

    def _fill_captcha_field(self, captcha_text):
        """
        Find and fill the captcha input field.
        """
        try:
            # Common selectors for captcha input field
            captcha_selectors = [
                "input[name='captcha']",
                ".auth-form__captcha-field",
                ".auth-form_captcha-field",
                "input[name*='captcha']",
                "input[id*='captcha']",
                "input[placeholder*='captcha']",
                "input[placeholder*='Captcha']",
                ".captcha-input",
                "#captcha",
                "#captchaCode"
            ]

            for selector in captcha_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            # Clear and fill the field
                            element.clear()
                            element.send_keys(captcha_text)

                            # Verify the value was set
                            if element.get_attribute('value') == captcha_text:
                                self.logger.info("Successfully filled captcha field")
                                return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.debug(f"Error in _fill_captcha_field: {str(e)}")
            return False

    def _submit_form(self):
        """
        Submit the validation form.
        """
        try:
            # Common selectors for submit button
            submit_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button.btn-primary",
                "button.submit",
                ".submit-btn",
                "#submit",
                "button:contains('Submit')",
                "button:contains('Verify')",
                "button:contains('Check')"
            ]

            for selector in submit_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            # Click the submit button
                            self.driver.execute_script("arguments[0].click();", element)

                            # Wait a moment for submission
                            time.sleep(2)

                            self.logger.info("Successfully submitted form")
                            return True
                except:
                    continue

            # Try submitting by pressing Enter on any form field
            try:
                form_elements = self.driver.find_elements(By.CSS_SELECTOR, "input, button")
                for element in form_elements:
                    if element.is_displayed():
                        element.submit()
                        time.sleep(2)
                        return True
            except:
                pass

            return False

        except Exception as e:
            self.logger.debug(f"Error in _submit_form: {str(e)}")
            return False

    def extract_result_data(self):
        """
        Extract validation results from the response page.

        Returns:
            dict: Structured validation results
        """
        try:
            self.logger.info("Extracting validation results from page")

            # Wait for results to load
            time.sleep(3)

            # Try different extraction methods
            result_data = self._extract_structured_results()
            if result_data:
                return result_data

            # Fallback to generic text extraction
            result_data = self._extract_generic_results()
            if result_data:
                return result_data

            # Last resort - capture page content
            return self._extract_raw_page_content()

        except Exception as e:
            self.logger.error(f"Error extracting result data: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to extract results: {str(e)}",
                "error_type": "ExtractionError"
            }

    def _extract_structured_results(self):
        """
        Extract results from structured elements on the page.
        """
        try:
            result_data = {
                "success": False,
                "validation_data": {},
                "raw_content": ""
            }

            # Look for success indicators
            success_selectors = [
                ".success",
                ".valid",
                ".result-success",
                "[class*='success']",
                "[class*='valid']"
            ]

            # Look for error indicators
            error_selectors = [
                ".error",
                ".invalid",
                ".result-error",
                "[class*='error']",
                "[class*='invalid']",
                ".alert-danger"
            ]

            # Check for success
            for selector in success_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            if text and any(word in text.lower() for word in ['valid', 'success', 'verified']):
                                result_data["success"] = True
                                result_data["validation_data"]["status"] = "Valid"
                                result_data["validation_data"]["message"] = text
                                break
                except:
                    continue

            # Check for errors
            for selector in error_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            if text and any(word in text.lower() for word in ['invalid', 'error', 'not found']):
                                result_data["success"] = False
                                result_data["validation_data"]["status"] = "Invalid"
                                result_data["validation_data"]["message"] = text
                                break
                except:
                    continue

            # Extract additional details
            detail_selectors = [
                ".result-details",
                ".validation-details",
                ".aadhaar-details",
                ".details"
            ]

            for selector in detail_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            details = self._parse_details_element(element)
                            if details:
                                result_data["validation_data"].update(details)
                except:
                    continue

            # If we found any meaningful data, return it
            if result_data["validation_data"]:
                return result_data

            return None

        except Exception as e:
            self.logger.debug(f"Error in _extract_structured_results: {str(e)}")
            return None

    def _parse_details_element(self, element):
        """
        Parse details from a result element.
        """
        try:
            details = {}
            text = element.text.strip()

            # Look for key-value pairs
            lines = text.split('\n')
            for line in lines:
                if ':' in line:
                    key, value = line.split(':', 1)
                    details[key.strip()] = value.strip()

            return details

        except:
            return {}

    def _extract_generic_results(self):
        """
        Generic extraction of results from page text.
        """
        try:
            # Get all visible text from the page
            page_text = self.driver.find_element(By.TAG_NAME, "body").text

            result_data = {
                "success": False,
                "validation_data": {},
                "raw_content": page_text
            }

            # Look for success/failure indicators in text
            text_lower = page_text.lower()

            if any(word in text_lower for word in ['aadhaar number is valid', 'valid aadhaar', 'verification successful']):
                result_data["success"] = True
                result_data["validation_data"]["status"] = "Valid"
            elif any(word in text_lower for word in ['aadhaar number is invalid', 'invalid aadhaar', 'not found', 'verification failed']):
                result_data["success"] = False
                result_data["validation_data"]["status"] = "Invalid"

            # Extract any structured information
            lines = page_text.split('\n')
            for line in lines:
                line = line.strip()
                if ':' in line and len(line) < 200:  # Reasonable length for a detail line
                    key, value = line.split(':', 1)
                    result_data["validation_data"][key.strip()] = value.strip()

            return result_data

        except Exception as e:
            self.logger.debug(f"Error in _extract_generic_results: {str(e)}")
            return None

    def _extract_raw_page_content(self):
        """
        Extract raw page content as fallback.
        """
        try:
            page_source = self.driver.page_source
            page_text = self.driver.find_element(By.TAG_NAME, "body").text

            return {
                "success": False,
                "validation_data": {
                    "raw_html": page_source,
                    "raw_text": page_text
                },
                "error": "Could not parse structured results",
                "error_type": "ParseError"
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to extract any content: {str(e)}",
                "error_type": "ExtractionError"
            }

    def handle_captcha_refresh(self):
        """
        Handle captcha refresh if needed.

        Returns:
            bool: True if captcha was refreshed successfully
        """
        try:
            # Look for refresh/reload captcha button
            refresh_selectors = [
                ".captcha-refresh",
                ".refresh-captcha",
                "button[onclick*='captcha']",
                "button[onclick*='refresh']",
                ".fa-refresh",
                ".fa-reload"
            ]

            for selector in refresh_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            time.sleep(2)  # Wait for new captcha to load
                            self.logger.info("Successfully refreshed captcha")
                            return True
                except:
                    continue

            # If no refresh button found, try reloading the page
            self.logger.info("No captcha refresh button found, reloading page")
            self.driver.refresh()
            time.sleep(3)
            return True

        except Exception as e:
            self.logger.error(f"Error refreshing captcha: {str(e)}")
            return False

    def wait_for_page_load(self, timeout=30):
        """
        Wait for page to fully load.

        Args:
            timeout (int): Maximum time to wait in seconds

        Returns:
            bool: True if page loaded successfully
        """
        try:
            # Wait for document ready state
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )

            # Additional wait for dynamic content
            time.sleep(2)

            return True

        except TimeoutException:
            self.logger.warning(f"Page load timeout after {timeout} seconds")
            return False
        except Exception as e:
            self.logger.error(f"Error waiting for page load: {str(e)}")
            return False

    def check_for_errors(self):
        """
        Check if there are any error messages on the page.

        Returns:
            str or None: Error message if found
        """
        try:
            error_selectors = [
                ".error",
                ".alert-danger",
                ".error-message",
                "[class*='error']",
                ".invalid-feedback"
            ]

            for selector in error_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            if text and len(text) > 0:
                                return text
                except:
                    continue

            return None

        except Exception as e:
            self.logger.debug(f"Error checking for page errors: {str(e)}")
            return None

    def is_captcha_invalid_error(self, error_message):
        """
        Check if error message indicates invalid captcha.

        Args:
            error_message (str): Error message to check

        Returns:
            bool: True if error is related to invalid captcha
        """
        if not error_message:
            return False

        error_lower = error_message.lower()
        captcha_error_keywords = [
            'captcha',
            'verification code',
            'invalid code',
            'wrong code',
            'incorrect code'
        ]

        return any(keyword in error_lower for keyword in captcha_error_keywords)

    def create_error_response(self, error_message, error_type="GeneralError", aadhaar_number=None):
        """
        Create standardized error response.

        Args:
            error_message (str): Error description
            error_type (str): Type of error
            aadhaar_number (str): Aadhaar number being checked

        Returns:
            dict: Standardized error response
        """
        return {
            "success": False,
            "error": error_message,
            "error_type": error_type,
            "aadhaar_number": aadhaar_number,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    def create_success_response(self, validation_data, aadhaar_number):
        """
        Create standardized success response.

        Args:
            validation_data (dict): Extracted validation data
            aadhaar_number (str): Aadhaar number that was checked

        Returns:
            dict: Standardized success response
        """
        return {
            "success": True,
            "aadhaar_number": aadhaar_number,
            "validation_data": validation_data,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    def check_aadhaar_validity(self, aadhaar_number, max_retries=3):
        """
        Main method to check Aadhaar validity with retry logic.

        Args:
            aadhaar_number (str): Aadhaar number to validate
            max_retries (int): Maximum number of retry attempts

        Returns:
            dict: Validation results or error information
        """
        try:
            self.logger.info(f"Starting Aadhaar validity check for: {aadhaar_number}")

            # Validate Aadhaar number format
            if not self.validate_aadhaar_number(aadhaar_number):
                return self.create_error_response(
                    "Invalid Aadhaar number format. Must be 12 digits.",
                    "ValidationError",
                    aadhaar_number
                )

            # Clean Aadhaar number
            clean_aadhaar = re.sub(r'[\s-]', '', str(aadhaar_number))

            # Attempt validation with retries
            for attempt in range(max_retries):
                self.logger.info(f"Attempt {attempt + 1} of {max_retries}")

                try:
                    # Navigate to website
                    if not self.navigate_to_website():
                        if attempt == max_retries - 1:
                            return self.create_error_response(
                                "Failed to load UIDAI website",
                                "NavigationError",
                                clean_aadhaar
                            )
                        continue

                    # Wait for page to load completely
                    if not self.wait_for_page_load():
                        if attempt == max_retries - 1:
                            return self.create_error_response(
                                "Page load timeout",
                                "TimeoutError",
                                clean_aadhaar
                            )
                        continue

                    # Extract captcha image
                    captcha_image = self.extract_captcha_image()
                    if not captcha_image:
                        self.logger.warning("Failed to extract captcha image")
                        if attempt < max_retries - 1:
                            self.handle_captcha_refresh()
                            time.sleep(2)
                            continue
                        else:
                            return self.create_error_response(
                                "Failed to extract captcha image",
                                "CaptchaExtractionError",
                                clean_aadhaar
                            )

                    # Solve captcha using advanced OCR system
                    self.logger.info("🔍 Starting advanced captcha solving...")
                    captcha_text = self.solve_captcha(captcha_image)

                    if not captcha_text:
                        self.logger.warning("❌ Advanced captcha solving failed")
                        if attempt < max_retries - 1:
                            self.logger.info("🔄 Refreshing captcha and retrying...")
                            self.handle_captcha_refresh()
                            time.sleep(2)
                            continue
                        else:
                            return self.create_error_response(
                                "Failed to solve captcha after multiple attempts with advanced OCR",
                                "CaptchaSolvingError",
                                clean_aadhaar
                            )
                    else:
                        self.logger.info(f"✅ Captcha solved successfully: '{captcha_text}'")

                    # Fill and submit form
                    if not self.fill_aadhaar_form(clean_aadhaar, captcha_text):
                        self.logger.warning("Failed to fill and submit form")
                        if attempt < max_retries - 1:
                            self.driver.refresh()
                            time.sleep(3)
                            continue
                        else:
                            return self.create_error_response(
                                "Failed to submit validation form",
                                "FormSubmissionError",
                                clean_aadhaar
                            )

                    # Wait for results
                    if not self.wait_for_page_load(timeout=15):
                        self.logger.warning("Timeout waiting for results")
                        if attempt < max_retries - 1:
                            continue

                    # Check for errors on the page
                    page_error = self.check_for_errors()
                    if page_error:
                        self.logger.warning(f"Page error detected: {page_error}")

                        # If it's a captcha error, retry
                        if self.is_captcha_invalid_error(page_error) and attempt < max_retries - 1:
                            self.logger.info("Captcha error detected, retrying...")
                            self.driver.refresh()
                            time.sleep(3)
                            continue
                        else:
                            return self.create_error_response(
                                page_error,
                                "ValidationError",
                                clean_aadhaar
                            )

                    # Extract results
                    result_data = self.extract_result_data()

                    if result_data and result_data.get("success") is not False:
                        # Success case
                        self.logger.info("Successfully validated Aadhaar")
                        return self.create_success_response(
                            result_data.get("validation_data", {}),
                            clean_aadhaar
                        )
                    elif result_data and "validation_data" in result_data:
                        # Got results but validation failed
                        self.logger.info("Aadhaar validation completed with negative result")
                        response = self.create_success_response(
                            result_data["validation_data"],
                            clean_aadhaar
                        )
                        response["success"] = False
                        return response
                    else:
                        # Could not extract meaningful results
                        if attempt < max_retries - 1:
                            self.logger.warning("Could not extract results, retrying...")
                            self.driver.refresh()
                            time.sleep(3)
                            continue
                        else:
                            return self.create_error_response(
                                "Could not extract validation results",
                                "ResultExtractionError",
                                clean_aadhaar
                            )

                except TimeoutException:
                    self.logger.warning(f"Timeout on attempt {attempt + 1}")
                    if attempt == max_retries - 1:
                        return self.create_error_response(
                            "Operation timed out",
                            "TimeoutError",
                            clean_aadhaar
                        )
                    continue

                except WebDriverException as e:
                    self.logger.error(f"WebDriver error on attempt {attempt + 1}: {str(e)}")
                    if attempt == max_retries - 1:
                        return self.create_error_response(
                            f"WebDriver error: {str(e)}",
                            "WebDriverError",
                            clean_aadhaar
                        )
                    continue

                except Exception as e:
                    self.logger.error(f"Unexpected error on attempt {attempt + 1}: {str(e)}")
                    if attempt == max_retries - 1:
                        return self.create_error_response(
                            f"Unexpected error: {str(e)}",
                            "UnexpectedError",
                            clean_aadhaar
                        )
                    continue

            # If we get here, all retries failed
            return self.create_error_response(
                f"All {max_retries} attempts failed",
                "MaxRetriesExceeded",
                clean_aadhaar
            )

        except Exception as e:
            self.logger.error(f"Critical error in check_aadhaar_validity: {str(e)}")
            return self.create_error_response(
                f"Critical error: {str(e)}",
                "CriticalError",
                aadhaar_number
            )


def main():
    """
    Main function to demonstrate usage of the AadhaarValidityChecker.
    """
    # Test Aadhaar number provided in requirements
    test_aadhaar = "************"

    print("=" * 60)
    print("Aadhaar Validity Checker - Demo")
    print("=" * 60)

    # Create checker instance
    print("\n1. Initializing checker...")
    checker = AadhaarValidityChecker(headless=False, debug=True)

    try:
        print(f"\n2. Checking Aadhaar validity for: {test_aadhaar}")
        print("This may take a few moments...")

        # Check validity with retry logic
        result = checker.check_aadhaar_validity(test_aadhaar, max_retries=3)

        print("\n3. Results:")
        print("-" * 40)
        print(json.dumps(result, indent=2))

        if result.get("success"):
            print("\n✅ Aadhaar validation successful!")
        else:
            print(f"\n❌ Aadhaar validation failed: {result.get('error', 'Unknown error')}")

    except KeyboardInterrupt:
        print("\n\n⚠️ Operation cancelled by user")

    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")

    finally:
        print("\n4. Cleaning up...")
        checker.close()
        print("✅ Cleanup complete")


def test_multiple_scenarios():
    """
    Test function to demonstrate various scenarios.
    """
    test_cases = [
        ("************", "Valid test Aadhaar"),
        ("123456789012", "Invalid test Aadhaar"),
        ("invalid", "Invalid format"),
        ("", "Empty input")
    ]

    print("=" * 60)
    print("Aadhaar Validity Checker - Multiple Test Scenarios")
    print("=" * 60)

    checker = AadhaarValidityChecker(headless=True, debug=False)

    try:
        for i, (aadhaar, description) in enumerate(test_cases, 1):
            print(f"\n{i}. Testing: {description}")
            print(f"   Aadhaar: {aadhaar}")

            result = checker.check_aadhaar_validity(aadhaar, max_retries=2)

            if result.get("success"):
                print(f"   ✅ Result: Valid")
            else:
                print(f"   ❌ Result: {result.get('error', 'Invalid')}")

            print(f"   Error Type: {result.get('error_type', 'N/A')}")

    except Exception as e:
        print(f"\n❌ Test error: {str(e)}")

    finally:
        checker.close()


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "--test":
            test_multiple_scenarios()
        elif sys.argv[1] == "--help":
            print("Usage:")
            print("  python aadhaar_checker.py           # Run demo with test Aadhaar")
            print("  python aadhaar_checker.py --test    # Run multiple test scenarios")
            print("  python aadhaar_checker.py --help    # Show this help")
        else:
            # Custom Aadhaar number provided
            custom_aadhaar = sys.argv[1]
            checker = AadhaarValidityChecker(headless=False, debug=True)
            try:
                result = checker.check_aadhaar_validity(custom_aadhaar)
                print(json.dumps(result, indent=2))
            finally:
                checker.close()
    else:
        main()

#!/usr/bin/env python3
"""
Simple test to verify form field detection and interaction
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time


def simple_form_test():
    """Test basic form interaction without captcha solving."""
    print("🧪 Testing basic form interaction...")
    
    # Setup Chrome options
    options = Options()
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")
    
    driver = None
    try:
        # Create WebDriver
        driver = webdriver.Chrome(options=options)
        driver.implicitly_wait(10)
        
        print("✅ WebDriver created successfully")
        
        # Navigate to UIDAI website
        url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        print(f"🌐 Navigating to: {url}")
        
        driver.get(url)
        
        # Wait for page to load
        WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        time.sleep(3)  # Additional wait for dynamic content
        
        print("✅ Page loaded successfully")
        
        # Test finding Aadhaar input field
        print("\n🔍 Testing Aadhaar input field detection:")
        
        aadhaar_selectors = [
            "input[name='uid']",
            "input[name*='aadhaar']",
            "input[type='text']"
        ]
        
        aadhaar_field = None
        for selector in aadhaar_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    for element in elements:
                        if element.is_displayed():
                            aadhaar_field = element
                            print(f"  ✅ Found Aadhaar field with selector: {selector}")
                            break
                if aadhaar_field:
                    break
            except Exception as e:
                print(f"  ❌ Error with selector {selector}: {e}")
        
        if aadhaar_field:
            # Test filling Aadhaar field
            test_aadhaar = "************"
            try:
                aadhaar_field.clear()
                aadhaar_field.send_keys(test_aadhaar)
                
                # Verify the value was set
                if aadhaar_field.get_attribute('value') == test_aadhaar:
                    print(f"  ✅ Successfully filled Aadhaar field with: {test_aadhaar}")
                else:
                    print(f"  ⚠️ Aadhaar field value mismatch")
            except Exception as e:
                print(f"  ❌ Error filling Aadhaar field: {e}")
        else:
            print("  ❌ No Aadhaar input field found")
        
        # Test finding captcha input field
        print("\n🔍 Testing captcha input field detection:")
        
        captcha_selectors = [
            "input[name='captcha']",
            "input[name*='captcha']",
            ".auth-form__captcha-field",
            ".auth-form_captcha-field"
        ]
        
        captcha_field = None
        for selector in captcha_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    for element in elements:
                        if element.is_displayed():
                            captcha_field = element
                            print(f"  ✅ Found captcha field with selector: {selector}")
                            break
                if captcha_field:
                    break
            except Exception as e:
                print(f"  ❌ Error with selector {selector}: {e}")
        
        if captcha_field:
            # Test filling captcha field
            test_captcha = "TEST123"
            try:
                captcha_field.clear()
                captcha_field.send_keys(test_captcha)
                
                # Verify the value was set
                if captcha_field.get_attribute('value') == test_captcha:
                    print(f"  ✅ Successfully filled captcha field with: {test_captcha}")
                else:
                    print(f"  ⚠️ Captcha field value mismatch")
            except Exception as e:
                print(f"  ❌ Error filling captcha field: {e}")
        else:
            print("  ❌ No captcha input field found")
        
        # Test finding submit button
        print("\n🔍 Testing submit button detection:")
        
        submit_selectors = [
            "button[type='submit']",
            "input[type='submit']",
            "button:contains('Proceed')",
            "button.btn-primary",
            "button"
        ]
        
        submit_button = None
        for selector in submit_selectors:
            try:
                if "contains" in selector:
                    # Use XPath for text-based selection
                    elements = driver.find_elements(By.XPATH, "//button[contains(text(), 'Proceed')]")
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if elements:
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            submit_button = element
                            print(f"  ✅ Found submit button with selector: {selector}")
                            print(f"     Button text: '{element.text}'")
                            break
                if submit_button:
                    break
            except Exception as e:
                print(f"  ❌ Error with selector {selector}: {e}")
        
        if not submit_button:
            print("  ❌ No submit button found")
        
        # Look for captcha container elements
        print("\n🔍 Testing captcha container detection:")
        
        container_selectors = [
            ".auth-form__captcha-field-container",
            ".auth-form_captcha-field-container",
            "[class*='captcha']"
        ]
        
        for selector in container_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"  ✅ Found {len(elements)} elements with selector: {selector}")
                    for i, element in enumerate(elements[:2]):  # Show first 2
                        class_attr = element.get_attribute("class") or "no-class"
                        tag_name = element.tag_name
                        print(f"     Element {i+1}: <{tag_name}> class='{class_attr}'")
                        
                        # Check if this container has any images or canvas
                        inner_images = element.find_elements(By.TAG_NAME, "img")
                        inner_canvas = element.find_elements(By.TAG_NAME, "canvas")
                        inner_divs = element.find_elements(By.TAG_NAME, "div")
                        
                        print(f"       Contains: {len(inner_images)} images, {len(inner_canvas)} canvas, {len(inner_divs)} divs")
                        
                        # Check for background images
                        bg_image = element.value_of_css_property("background-image")
                        if bg_image and bg_image != "none":
                            print(f"       Background image: {bg_image[:100]}...")
                else:
                    print(f"  ❌ No elements found with selector: {selector}")
            except Exception as e:
                print(f"  ❌ Error with selector {selector}: {e}")
        
        print("\n✅ Form interaction test complete!")
        
        # Keep browser open for manual inspection
        input("\nPress Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        
    finally:
        if driver:
            driver.quit()


if __name__ == "__main__":
    simple_form_test()

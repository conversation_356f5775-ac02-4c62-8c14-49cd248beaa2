# Aadhaar Validity Checker

A Python automation script to check Aadhaar validity by scraping the official UIDAI website without using APIs.

## Features

- Automated web scraping of UIDAI website
- OCR-based captcha solving using Tesseract
- Retry logic for failed attempts
- Headless and visible browser modes
- Comprehensive error handling
- JSON formatted results

## System Requirements

### Software Dependencies
- Python 3.7+
- Google Chrome browser
- ChromeDriver (automatically managed)
- Tesseract OCR engine

### Installing Tesseract OCR

#### Windows
1. Download Tesseract installer from: https://github.com/UB-Mannheim/tesseract/wiki
2. Install to default location (usually `C:\Program Files\Tesseract-OCR\`)
3. Add Tesseract to your system PATH

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install tesseract-ocr
```

#### macOS
```bash
brew install tesseract
```

## Installation

1. Clone or download this repository
2. Install Python dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage
```python
from aadhaar_checker import AadhaarValidityChecker

# Create checker instance
checker = AadhaarValidityChecker(headless=True)

# Check Aadhaar validity
result = checker.check_aadhaar_validity("************")

# Print results
print(result)

# Clean up
checker.close()
```

### Advanced Usage
```python
# Use visible browser for debugging
checker = AadhaarValidityChecker(headless=False)

# Check with custom retry count
result = checker.check_aadhaar_validity("************", max_retries=5)
```

## Configuration Options

- `headless`: Run browser in headless mode (default: False)
- `max_retries`: Maximum number of retry attempts for captcha solving (default: 3)

## Output Format

### Success Response
```json
{
    "success": true,
    "aadhaar_number": "************",
    "validation_data": {
        "status": "Valid",
        "details": "..."
    },
    "timestamp": "2024-01-01 12:00:00"
}
```

### Error Response
```json
{
    "success": false,
    "error": "Captcha solving failed after 3 attempts",
    "error_type": "CaptchaError",
    "timestamp": "2024-01-01 12:00:00"
}
```

## Troubleshooting

1. **Tesseract not found**: Ensure Tesseract is installed and in your system PATH
2. **ChromeDriver issues**: The script uses webdriver-manager to automatically handle ChromeDriver
3. **Captcha solving failures**: Try increasing max_retries or running in visible mode for debugging
4. **Network timeouts**: Check your internet connection and firewall settings

## Legal Notice

This tool is for educational and legitimate verification purposes only. Please ensure compliance with UIDAI terms of service and applicable laws when using this script.

#!/usr/bin/env python3
"""
ChromeDriver Fix Script

This script helps resolve ChromeDriver issues by clearing cache and testing different approaches.
"""

import os
import shutil
import sys
from pathlib import Path


def clear_chromedriver_cache():
    """Clear ChromeDriver cache directories."""
    print("🧹 Clearing ChromeDriver cache...")
    
    cache_paths = [
        os.path.expanduser("~/.wdm"),
        os.path.expanduser("~/AppData/Local/Temp/.wdm"),
        os.path.expanduser("~/Library/Caches/.wdm"),
        os.path.join(os.getcwd(), ".wdm")
    ]
    
    cleared_count = 0
    for cache_path in cache_paths:
        if os.path.exists(cache_path):
            try:
                shutil.rmtree(cache_path)
                print(f"  ✅ Cleared: {cache_path}")
                cleared_count += 1
            except Exception as e:
                print(f"  ❌ Failed to clear {cache_path}: {e}")
    
    if cleared_count == 0:
        print("  ℹ️ No cache directories found to clear")
    else:
        print(f"  🎉 Cleared {cleared_count} cache directories")


def test_chrome_installation():
    """Test if Chrome is properly installed."""
    print("\n🔍 Testing Chrome installation...")
    
    try:
        import subprocess
        result = subprocess.run(['chrome', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"  ✅ Chrome found: {result.stdout.strip()}")
            return True
    except:
        pass
    
    try:
        result = subprocess.run(['google-chrome', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"  ✅ Chrome found: {result.stdout.strip()}")
            return True
    except:
        pass
    
    # Check common Chrome installation paths on Windows
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
    ]
    
    for chrome_path in chrome_paths:
        if os.path.exists(chrome_path):
            try:
                result = subprocess.run([chrome_path, '--version'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"  ✅ Chrome found at: {chrome_path}")
                    print(f"     Version: {result.stdout.strip()}")
                    return True
            except:
                continue
    
    print("  ❌ Chrome not found or not accessible")
    return False


def test_webdriver_manager():
    """Test WebDriver Manager functionality."""
    print("\n🧪 Testing WebDriver Manager...")
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        
        print("  📦 WebDriver Manager imported successfully")
        
        # Try to get ChromeDriver path
        driver_path = ChromeDriverManager().install()
        print(f"  ✅ ChromeDriver path: {driver_path}")
        
        # Check if the file exists and is executable
        if os.path.exists(driver_path):
            print(f"  ✅ ChromeDriver file exists")
            
            # Check file size
            file_size = os.path.getsize(driver_path)
            print(f"  📊 File size: {file_size:,} bytes")
            
            if file_size < 1000:  # Less than 1KB is suspicious
                print(f"  ⚠️ File size seems too small, might be corrupted")
                return False
            
            return True
        else:
            print(f"  ❌ ChromeDriver file does not exist at: {driver_path}")
            return False
            
    except Exception as e:
        print(f"  ❌ WebDriver Manager test failed: {e}")
        return False


def test_selenium_basic():
    """Test basic Selenium functionality."""
    print("\n🔧 Testing basic Selenium functionality...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        print("  📦 Selenium imported successfully")
        
        # Test Chrome options
        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        print("  ✅ Chrome options configured")
        
        # Try to create WebDriver instance
        try:
            driver = webdriver.Chrome(options=options)
            print("  ✅ WebDriver created successfully")
            
            # Test basic navigation
            driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")
            title = driver.title
            print(f"  ✅ Navigation test passed: {title}")
            
            driver.quit()
            print("  ✅ WebDriver closed successfully")
            return True
            
        except Exception as e:
            print(f"  ❌ WebDriver creation failed: {e}")
            return False
            
    except Exception as e:
        print(f"  ❌ Selenium test failed: {e}")
        return False


def main():
    """Main function to run all tests and fixes."""
    print("=" * 60)
    print("ChromeDriver Fix & Test Script")
    print("=" * 60)
    
    # Step 1: Clear cache
    clear_chromedriver_cache()
    
    # Step 2: Test Chrome
    chrome_ok = test_chrome_installation()
    
    # Step 3: Test WebDriver Manager
    wdm_ok = test_webdriver_manager()
    
    # Step 4: Test Selenium
    selenium_ok = test_selenium_basic()
    
    # Summary
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    print(f"Chrome Installation: {'✅ OK' if chrome_ok else '❌ FAILED'}")
    print(f"WebDriver Manager:   {'✅ OK' if wdm_ok else '❌ FAILED'}")
    print(f"Selenium Basic:      {'✅ OK' if selenium_ok else '❌ FAILED'}")
    
    if all([chrome_ok, wdm_ok, selenium_ok]):
        print("\n🎉 All tests passed! You should be able to run the Aadhaar checker now.")
        print("\nTry running: python test_aadhaar_checker.py basic")
    else:
        print("\n⚠️ Some tests failed. Here are some suggestions:")
        
        if not chrome_ok:
            print("  • Install Google Chrome from: https://www.google.com/chrome/")
        
        if not wdm_ok:
            print("  • Try reinstalling webdriver-manager: pip install --upgrade webdriver-manager")
            print("  • Check your internet connection")
        
        if not selenium_ok:
            print("  • Try reinstalling selenium: pip install --upgrade selenium")
            print("  • Check if antivirus is blocking ChromeDriver")


if __name__ == "__main__":
    main()
